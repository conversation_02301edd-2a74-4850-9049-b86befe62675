# LocPay Backend - Sistema de Antecipação de Aluguéis

Backend completo para sistema de antecipação de aluguéis desenvolvido com **NestJS**, **TypeScript**, **Prisma** e **PostgreSQL**.

## 🏗️ Arquitetura

- **Framework**: NestJS com TypeScript
- **Banco de Dados**: PostgreSQL com Prisma ORM
- **Autenticação**: JWT com códigos de verificação via WhatsApp
- **Integrações**: N8N para automações e Google Drive para armazenamento
- **Validação**: Class-validator com pipes globais
- **Tratamento de Erros**: Interceptor global com logs estruturados

## 🚀 Funcionalidades

### 1. **Autenticação Segura**

- Login por CPF com criação automática de usuários
- Verificação por código de 6 dígitos via WhatsApp (N8N)
- JWT tokens com expiração configurável
- Validação robusta de CPF, telefone e dados

### 2. **Fluxo de Antecipação**

- **Criação**: Upload e extração automática de dados do PDF do contrato
- **Confirmação**: Validação dos dados extraídos pelo usuário
- **Proposta**: Geração automática via API externa integrada
- **Documentação**: Upload de chave PIX e documento de identidade
- **Revisão**: Sistema de aprovação/rejeição manual

### 3. **Integrações Robustas**

- **N8N**: Envio de WhatsApp, extração de PDF e geração de propostas
- **Google Drive**: Armazenamento organizado por operação
- **APIs Externas**: Integração para cálculo de propostas

### 4. **Gestão Completa**

- Histórico detalhado de status de cada operação
- Logs estruturados para auditoria
- Paginação e filtros para administradores
- Validação completa de arquivos e dados

## 📁 Estrutura do Projeto

```
src/
├── auth/                     # Autenticação e autorização
│   ├── guards/              # JWT guards
│   ├── dto/                 # DTOs de login e verificação
│   ├── auth.service.ts      # Lógica de autenticação
│   └── auth.controller.ts   # Endpoints de auth
├── rental-advance/          # Core business logic
│   ├── dto/                 # DTOs das operações
│   ├── enums/              # Enums de status
│   ├── rental-advance.service.ts
│   └── rental-advance.controller.ts
├── real-estate/            # Gestão de imobiliárias
├── integrations/           # Integrações externas
│   ├── n8n/               # Automações N8N
│   └── drive/             # Google Drive
├── common/                 # Utilitários compartilhados
│   ├── utils/             # Validações e helpers
│   ├── interceptors/      # Interceptors globais
│   └── decorators/        # Decorators customizados
└── prisma.service.ts      # Configuração do Prisma
```

## 🛠️ Instalação

### 1. **Clone e instale dependências**

```bash
git clone <repository>
cd locpay-backend
yarn install
```

### 2. **Configure as variáveis de ambiente**

```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

### 3. **Configure o banco de dados**

```bash
# Execute as migrações
yarn prisma migrate dev

# (Opcional) Popule com dados de teste
yarn prisma db seed
```

### 4. **Execute a aplicação**

```bash
# Desenvolvimento
yarn start:dev

# Produção
yarn build
yarn start:prod
```

## 🔧 Configuração

### **Banco de Dados PostgreSQL**

```env
DATABASE_URL="postgresql://user:password@localhost:5432/locpay_db"
```

### **JWT Authentication**

```env
JWT_SECRET="your-super-secret-jwt-key"
```

### **Integração N8N**

```env
N8N_WEBHOOK_URL="https://your-n8n-instance.com/webhook"
```

### **Google Drive**

```env
DRIVE_ROOT_FOLDER_ID="your-google-drive-folder-id"
GOOGLE_SERVICE_ACCOUNT_JSON='{"type":"service_account",...}'
```

## 📊 API Endpoints

### **Autenticação**

- `POST /api/auth/login` - Login/cadastro por CPF
- `POST /api/auth/verify` - Verificação do código
- `POST /api/auth/resend-code` - Reenvio do código

### **Operações de Antecipação**

- `GET /api/rental-advance` - Listar operações do usuário
- `POST /api/rental-advance/create` - Criar nova operação
- `POST /api/rental-advance/confirm-data` - Confirmar dados extraídos
- `POST /api/rental-advance/request-proposal` - Solicitar proposta
- `POST /api/rental-advance/final-confirmation` - Confirmação final

### **Administração**

- `GET /api/rental-advance/admin/pending-reviews` - Operações para revisão
- `POST /api/rental-advance/admin/review-decision` - Aprovar/rejeitar

### **Imobiliárias**

- `GET /api/real-estate` - Listar imobiliárias
- `POST /api/real-estate` - Cadastrar imobiliária

## 🔄 Fluxo de Estados

```
created → pdf_extracted → data_confirmed → pending_proposal 
  → proposal_sent → docs_uploaded → awaiting_review 
  → approved/rejected
```

## 🧪 Testes

```bash
# Testes unitários
yarn test

# Testes e2e
yarn test:e2e

# Cobertura
yarn test:cov
```

## 📝 Validações Implementadas

- **CPF**: Validação completa com dígitos verificadores
- **CNPJ**: Validação para imobiliárias
- **Telefone**: Formato brasileiro (10-11 dígitos)
- **Email**: Regex padrão RFC
- **Chaves PIX**: Validação por tipo (CPF, email, telefone, aleatória)
- **Arquivos**: Tipo MIME e tamanho máximo

## 🔒 Segurança

- Sanitização automática de inputs
- Validação de tipos de arquivo
- JWT com expiração
- Logs estruturados para auditoria
- Rate limiting configurável
- CORS configurado

## 🆕 Funcionalidades Recém-Implementadas

### 🔥 Cache com Redis

- Cache distribuído para melhor performance
- Cache automático em operações de consulta
- Invalidação inteligente de cache
- Configuração flexível via variáveis de ambiente

### 🚦 Rate Limiting Avançado

- Rate limiting diferenciado por tipo de operação
- Configurações específicas para autenticação, uploads e operações sensíveis
- Proteção contra ataques de força bruta
- Logs detalhados para monitoramento
