import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

export default function ContactForm() {
  return (
    <section id="contato" className="py-16 bg-gray-100">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12">
          Entre Já em Contato e Receba até 12 Meses de Aluguel Imediatamente
        </h2>
        <form className="max-w-md mx-auto space-y-4">
          <Input type="text" placeholder="Nome" required className="focus:ring-2 focus:ring-blue-500" />
          <Input type="email" placeholder="E-mail" required className="focus:ring-2 focus:ring-blue-500" />
          <Input type="tel" placeholder="Telefone" required className="focus:ring-2 focus:ring-blue-500" />
          <Textarea placeholder="Mensagem" required className="focus:ring-2 focus:ring-blue-500" />
          <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 rounded-xl transition-colors">
            Enviar Mensagem
          </Button>
        </form>
      </div>
    </section>
  )
}
