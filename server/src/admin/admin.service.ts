import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { RentalAdvanceStatus } from '../rental-advance/enums/rental-status.enum';

interface SearchFilters {
  cpf?: string;
  name?: string;
}

interface RequestFilters {
  status?: string;
  realEstateId?: string;
  startDate?: string;
  endDate?: string;
}

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Verify admin authentication and return admin user info
   */
  async verifyAdminAuth(user: any) {
    try {
      // Double-check admin status from database (in case token is old)
      const adminUser = await this.prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          name: true,
          cpf: true,
          role: true,
          isAdmin: true,
          createdAt: true,
        },
      });

      if (!adminUser || (!adminUser.isAdmin && adminUser.role !== 'admin')) {
        throw new BadRequestException('Usuário não possui privilégios de administrador');
      }

      return {
        isAdmin: true,
        user: {
          id: adminUser.id,
          name: adminUser.name,
          cpf: adminUser.cpf,
          role: adminUser.role,
          isAdmin: adminUser.isAdmin,
          createdAt: adminUser.createdAt.toISOString(),
        },
        permissions: [
          'view_dashboard',
          'search_users',
          'manage_approvals',
          'view_requests',
          'export_data',
        ],
      };
    } catch (error) {
      this.logger.error('Error verifying admin auth:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Erro ao verificar privilégios de administrador');
    }
  }

  /**
   * Get dashboard statistics and KPIs
   */
  async getDashboardStatistics() {
    try {
      const [
        totalUsers,
        totalRequests,
        pendingApprovals,
        approvedRequests,
        rejectedRequests,
        requestsByStatus,
        recentRequests,
        monthlyStats,
      ] = await Promise.all([
        // Total users count
        this.prisma.user.count(),
        
        // Total requests count
        this.prisma.rentalAdvanceRequest.count(),
        
        // Pending approvals count
        this.prisma.rentalAdvanceRequest.count({
          where: { currentStatus: RentalAdvanceStatus.AWAITING_REVIEW },
        }),
        
        // Approved requests count
        this.prisma.rentalAdvanceRequest.count({
          where: { currentStatus: RentalAdvanceStatus.APPROVED },
        }),
        
        // Rejected requests count
        this.prisma.rentalAdvanceRequest.count({
          where: { currentStatus: RentalAdvanceStatus.REJECTED },
        }),
        
        // Requests by status
        this.prisma.rentalAdvanceRequest.groupBy({
          by: ['currentStatus'],
          _count: { currentStatus: true },
        }),
        
        // Recent requests (last 7 days)
        this.prisma.rentalAdvanceRequest.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        }),
        
        // Monthly statistics (last 12 months)
        this.getMonthlyStatistics(),
      ]);

      // Calculate approval rate
      const totalProcessed = approvedRequests + rejectedRequests;
      const approvalRate = totalProcessed > 0 ? (approvedRequests / totalProcessed) * 100 : 0;

      // Calculate total value of approved requests
      const approvedValue = await this.prisma.rentalAdvanceRequest.aggregate({
        where: { currentStatus: RentalAdvanceStatus.APPROVED },
        _sum: { proposalAmount: true },
      });

      return {
        overview: {
          totalUsers,
          totalRequests,
          pendingApprovals,
          approvedRequests,
          rejectedRequests,
          recentRequests,
          approvalRate: Math.round(approvalRate * 100) / 100,
          totalApprovedValue: approvedValue._sum.proposalAmount || 0,
        },
        statusBreakdown: requestsByStatus.map(item => ({
          status: item.currentStatus,
          count: item._count.currentStatus,
        })),
        monthlyStats,
      };
    } catch (error) {
      this.logger.error('Error getting dashboard statistics:', error);
      throw new BadRequestException('Erro ao buscar estatísticas do dashboard');
    }
  }

  /**
   * Get monthly statistics for the last 12 months
   */
  private async getMonthlyStatistics() {
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const monthlyData = await this.prisma.rentalAdvanceRequest.findMany({
      where: {
        createdAt: { gte: twelveMonthsAgo },
      },
      select: {
        createdAt: true,
        currentStatus: true,
        proposalAmount: true,
      },
    });

    // Group by month
    const monthlyStats = {};
    monthlyData.forEach(request => {
      const monthKey = request.createdAt.toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyStats[monthKey]) {
        monthlyStats[monthKey] = {
          month: monthKey,
          totalRequests: 0,
          approvedRequests: 0,
          totalValue: 0,
        };
      }
      monthlyStats[monthKey].totalRequests++;
      if (request.currentStatus === RentalAdvanceStatus.APPROVED) {
        monthlyStats[monthKey].approvedRequests++;
        monthlyStats[monthKey].totalValue += Number(request.proposalAmount || 0);
      }
    });

    return Object.values(monthlyStats).sort((a: any, b: any) => a.month.localeCompare(b.month));
  }

  /**
   * Search users by CPF or name
   */
  async searchUsers(filters: SearchFilters, page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;
      const where: any = {};

      if (filters.cpf) {
        // Remove any formatting from CPF
        const cleanCpf = filters.cpf.replace(/\D/g, '');
        where.cpf = { contains: cleanCpf };
      }

      if (filters.name) {
        where.name = { contains: filters.name, mode: 'insensitive' };
      }

      const [users, total] = await Promise.all([
        this.prisma.user.findMany({
          where,
          include: {
            _count: {
              select: { rentalAdvanceRequests: true },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.user.count({ where }),
      ]);

      return {
        users: users.map(user => ({
          id: user.id,
          name: user.name,
          cpf: user.cpf,
          phone: user.phone,
          role: user.role,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          requestCount: user._count.rentalAdvanceRequests,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error('Error searching users:', error);
      throw new BadRequestException('Erro ao buscar usuários');
    }
  }

  /**
   * Get all requests for a specific user
   */
  async getUserRequests(userId: string, page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      // First verify user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, name: true, cpf: true },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      const [requests, total] = await Promise.all([
        this.prisma.rentalAdvanceRequest.findMany({
          where: { userId },
          include: {
            realEstate: { select: { name: true, cnpj: true } },
            contractData: true,
            statusLogs: {
              orderBy: { createdAt: 'desc' },
              take: 5,
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.rentalAdvanceRequest.count({ where: { userId } }),
      ]);

      return {
        user,
        requests,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error('Error getting user requests:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Erro ao buscar solicitações do usuário');
    }
  }

  /**
   * Get enhanced request listing with filters
   */
  async getRequests(filters: RequestFilters, page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;
      const where: any = {};

      if (filters.status) {
        where.currentStatus = filters.status;
      }

      if (filters.realEstateId) {
        where.realEstateId = filters.realEstateId;
      }

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          where.createdAt.lte = new Date(filters.endDate);
        }
      }

      const [requests, total] = await Promise.all([
        this.prisma.rentalAdvanceRequest.findMany({
          where,
          include: {
            user: { select: { name: true, cpf: true, phone: true } },
            realEstate: { select: { name: true, cnpj: true } },
            contractData: true,
            statusLogs: {
              orderBy: { createdAt: 'desc' },
              take: 3,
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.rentalAdvanceRequest.count({ where }),
      ]);

      return {
        requests,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error('Error getting requests:', error);
      throw new BadRequestException('Erro ao buscar solicitações');
    }
  }

  /**
   * Get detailed user information
   */
  async getUserDetails(userId: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          _count: {
            select: { rentalAdvanceRequests: true },
          },
          rentalAdvanceRequests: {
            select: {
              id: true,
              currentStatus: true,
              rentAmount: true,
              proposalAmount: true,
              createdAt: true,
              realEstate: { select: { name: true } },
            },
            orderBy: { createdAt: 'desc' },
            take: 5,
          },
        },
      });

      if (!user) {
        throw new NotFoundException('Usuário não encontrado');
      }

      // Calculate user statistics
      const userStats = await this.prisma.rentalAdvanceRequest.groupBy({
        by: ['currentStatus'],
        where: { userId },
        _count: { currentStatus: true },
        _sum: { proposalAmount: true },
      });

      return {
        user: {
          id: user.id,
          name: user.name,
          cpf: user.cpf,
          phone: user.phone,
          role: user.role,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          totalRequests: user._count.rentalAdvanceRequests,
        },
        recentRequests: user.rentalAdvanceRequests,
        statistics: userStats.map(stat => ({
          status: stat.currentStatus,
          count: stat._count.currentStatus,
          totalValue: stat._sum.proposalAmount || 0,
        })),
      };
    } catch (error) {
      this.logger.error('Error getting user details:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Erro ao buscar detalhes do usuário');
    }
  }

  /**
   * Get all real estate companies for filtering
   */
  async getRealEstates() {
    try {
      const realEstates = await this.prisma.realEstate.findMany({
        select: {
          id: true,
          name: true,
          cnpj: true,
          _count: {
            select: { rentalAdvanceRequests: true },
          },
        },
        orderBy: { name: 'asc' },
      });

      return {
        realEstates: realEstates.map(re => ({
          id: re.id,
          name: re.name,
          cnpj: re.cnpj,
          requestCount: re._count.rentalAdvanceRequests,
        })),
      };
    } catch (error) {
      this.logger.error('Error getting real estates:', error);
      throw new BadRequestException('Erro ao buscar imobiliárias');
    }
  }

  /**
   * Search real estates with pagination and filters
   */
  async searchRealEstates(params: {
    page: number;
    limit: number;
    search?: string;
  }) {
    try {
      const { page, limit, search } = params;
      const skip = (page - 1) * limit;

      const where = search
        ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' as const } },
              { cnpj: { contains: search.replace(/\D/g, '') } },
            ],
          }
        : {};

      const [realEstates, total] = await Promise.all([
        this.prisma.realEstate.findMany({
          where,
          select: {
            id: true,
            name: true,
            cnpj: true,
            createdAt: true,
            _count: {
              select: { rentalAdvanceRequests: true },
            },
          },
          orderBy: { name: 'asc' },
          skip,
          take: limit,
        }),
        this.prisma.realEstate.count({ where }),
      ]);

      return {
        realEstates: realEstates.map(re => ({
          id: re.id,
          name: re.name,
          cnpj: re.cnpj,
          createdAt: re.createdAt,
          requestCount: re._count.rentalAdvanceRequests,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error('Error searching real estates:', error);
      throw new BadRequestException('Erro ao buscar imobiliárias');
    }
  }

  /**
   * Create new real estate
   */
  async createRealEstate(data: { name: string; cnpj: string }) {
    try {
      // Remove formatting from CNPJ
      const cleanCnpj = data.cnpj.replace(/\D/g, '');

      // Validate CNPJ format
      if (cleanCnpj.length !== 14) {
        throw new BadRequestException('CNPJ deve ter 14 dígitos');
      }

      // Check if CNPJ already exists
      const existingRealEstate = await this.prisma.realEstate.findUnique({
        where: { cnpj: cleanCnpj },
      });

      if (existingRealEstate) {
        throw new BadRequestException('CNPJ já cadastrado');
      }

      const realEstate = await this.prisma.realEstate.create({
        data: {
          name: data.name.trim(),
          cnpj: cleanCnpj,
        },
      });

      return {
        id: realEstate.id,
        name: realEstate.name,
        cnpj: realEstate.cnpj,
        createdAt: realEstate.createdAt,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('Error creating real estate:', error);
      throw new BadRequestException('Erro ao criar imobiliária');
    }
  }

  /**
   * Get real estate details with requests
   */
  async getRealEstateDetails(
    id: string,
    params: { page: number; limit: number },
  ) {
    try {
      const { page, limit } = params;
      const skip = (page - 1) * limit;

      // Get real estate info
      const realEstate = await this.prisma.realEstate.findUnique({
        where: { id },
        select: {
          id: true,
          name: true,
          cnpj: true,
          createdAt: true,
          _count: {
            select: { rentalAdvanceRequests: true },
          },
        },
      });

      if (!realEstate) {
        throw new BadRequestException('Imobiliária não encontrada');
      }

      // Get requests with pagination
      const [requests, total] = await Promise.all([
        this.prisma.rentalAdvanceRequest.findMany({
          where: { realEstateId: id },
          select: {
            id: true,
            rentAmount: true,
            monthsToAdvance: true,
            proposalAmount: true,
            currentStatus: true,
            createdAt: true,
            updatedAt: true,
            user: {
              select: {
                name: true,
                cpf: true,
                phone: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.rentalAdvanceRequest.count({
          where: { realEstateId: id },
        }),
      ]);

      return {
        realEstate: {
          id: realEstate.id,
          name: realEstate.name,
          cnpj: realEstate.cnpj,
          createdAt: realEstate.createdAt,
          totalRequests: realEstate._count.rentalAdvanceRequests,
        },
        requests: requests.map(req => ({
          id: req.id,
          rentAmount: req.rentAmount,
          monthsToAdvance: req.monthsToAdvance,
          proposalAmount: req.proposalAmount,
          currentStatus: req.currentStatus,
          createdAt: req.createdAt,
          updatedAt: req.updatedAt,
          user: {
            name: req.user.name,
            cpf: req.user.cpf,
            phone: req.user.phone,
          },
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('Error getting real estate details:', error);
      throw new BadRequestException('Erro ao buscar detalhes da imobiliária');
    }
  }

  /**
   * Export requests data for analysis
   */
  async exportRequests(filters: { status?: string; startDate?: string; endDate?: string }) {
    try {
      const where: any = {};

      if (filters.status) {
        where.currentStatus = filters.status;
      }

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          where.createdAt.lte = new Date(filters.endDate);
        }
      }

      const requests = await this.prisma.rentalAdvanceRequest.findMany({
        where,
        include: {
          user: { select: { name: true, cpf: true, phone: true } },
          realEstate: { select: { name: true, cnpj: true } },
          contractData: {
            select: {
              propertyAddress: true,
              landlordName: true,
              tenantName: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      // Format data for export
      const exportData = requests.map(request => ({
        id: request.id,
        userName: request.user.name,
        userCpf: request.user.cpf,
        userPhone: request.user.phone,
        realEstateName: request.realEstate?.name || 'N/A',
        realEstateCnpj: request.realEstate?.cnpj || 'N/A',
        rentAmount: Number(request.rentAmount),
        monthsToAdvance: request.monthsToAdvance,
        proposalAmount: Number(request.proposalAmount || 0),
        currentStatus: request.currentStatus,
        propertyAddress: request.contractData?.propertyAddress || 'N/A',
        landlordName: request.contractData?.landlordName || 'N/A',
        tenantName: request.contractData?.tenantName || 'N/A',
        createdAt: request.createdAt.toISOString(),
        updatedAt: request.updatedAt.toISOString(),
      }));

      return {
        data: exportData,
        count: exportData.length,
        exportedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error exporting requests:', error);
      throw new BadRequestException('Erro ao exportar solicitações');
    }
  }
}
