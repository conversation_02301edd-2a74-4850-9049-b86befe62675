-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "cpf" TEXT NOT NULL,
    "phone" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LoginCode" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "LoginCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RealEstate" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "cnpj" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RealEstate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RentalAdvanceRequest" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "rentAmount" DECIMAL(65,30) NOT NULL,
    "monthsToAdvance" INTEGER NOT NULL,
    "realEstateId" TEXT,
    "contractPdfUrl" TEXT,
    "currentStatus" TEXT,
    "proposalAmount" DECIMAL(65,30),
    "monthlyRentOffer" DECIMAL(65,30),
    "proposedMonths" INTEGER,
    "pixKey" TEXT,
    "identityDocUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RentalAdvanceRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RentalContractData" (
    "id" TEXT NOT NULL,
    "rentalRequestId" TEXT NOT NULL,
    "extractedData" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RentalContractData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RentalRequestStatusLog" (
    "id" TEXT NOT NULL,
    "rentalRequestId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RentalRequestStatusLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_cpf_key" ON "User"("cpf");

-- CreateIndex
CREATE INDEX "LoginCode_userId_idx" ON "LoginCode"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "RealEstate_cnpj_key" ON "RealEstate"("cnpj");

-- CreateIndex
CREATE INDEX "RentalAdvanceRequest_userId_idx" ON "RentalAdvanceRequest"("userId");

-- CreateIndex
CREATE INDEX "RentalAdvanceRequest_currentStatus_idx" ON "RentalAdvanceRequest"("currentStatus");

-- CreateIndex
CREATE UNIQUE INDEX "RentalContractData_rentalRequestId_key" ON "RentalContractData"("rentalRequestId");

-- CreateIndex
CREATE INDEX "RentalContractData_rentalRequestId_idx" ON "RentalContractData"("rentalRequestId");

-- CreateIndex
CREATE INDEX "RentalRequestStatusLog_rentalRequestId_idx" ON "RentalRequestStatusLog"("rentalRequestId");

-- CreateIndex
CREATE INDEX "RentalRequestStatusLog_status_idx" ON "RentalRequestStatusLog"("status");

-- AddForeignKey
ALTER TABLE "LoginCode" ADD CONSTRAINT "LoginCode_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RentalAdvanceRequest" ADD CONSTRAINT "RentalAdvanceRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RentalAdvanceRequest" ADD CONSTRAINT "RentalAdvanceRequest_realEstateId_fkey" FOREIGN KEY ("realEstateId") REFERENCES "RealEstate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RentalContractData" ADD CONSTRAINT "RentalContractData_rentalRequestId_fkey" FOREIGN KEY ("rentalRequestId") REFERENCES "RentalAdvanceRequest"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RentalRequestStatusLog" ADD CONSTRAINT "RentalRequestStatusLog_rentalRequestId_fkey" FOREIGN KEY ("rentalRequestId") REFERENCES "RentalAdvanceRequest"("id") ON DELETE CASCADE ON UPDATE CASCADE;
