interface SectionTitleProps {
  badge: string
  darkText: string
  lightText: string
  description: string
}

export default function SectionTitle({ badge, darkText, lightText, description }: SectionTitleProps) {
  return (
    <div className="text-center mb-12">
      <span className="inline-block px-4 py-1.5 bg-[#00A7E1]/10 text-[#1B3B5A] rounded-full text-sm font-medium mb-4">
        {badge}
      </span>
      <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4">
        <span className="text-[#1B3B5A]">{darkText}</span> <span className="text-[#00A7E1]">{lightText}</span>
      </h2>
      <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">{description}</p>
    </div>
  )
}
