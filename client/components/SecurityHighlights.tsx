import { Shield, Lock, UserCheck } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function SecurityHighlights() {
  const securityFeatures = [
    {
      icon: Shield,
      title: "Proteção de Dados 🛡️",
      description: "Utilizamos criptografia de ponta a ponta para proteger suas informações pessoais e financeiras.",
    },
    {
      icon: Lock,
      title: "Transações Seguras 🔐",
      description:
        "Todas as transações são processadas através de canais seguros e verificados, com tecnologia de última geração.",
    },
    {
      icon: UserCheck,
      title: "Verificação de Identidade 🕵️",
      description: "Realizamos verificações e análises rigorosas para prevenir fraudes e proteger nossa operação.",
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl md:text-5xl font-bold text-center mb-12">
          <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text">
            Sua Confiança é Nossa Prioridade 🛡️
          </span>
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          {securityFeatures.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-all border-t-4 border-t-[#6BBAED]">
              <CardHeader>
                <div className="h-12 w-12 rounded-lg bg-[#004B87]/10 flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-[#004B87]" />
                </div>
                <CardTitle className="text-[#004B87]">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
