/*
  Warnings:

  - You are about to alter the column `code` on the `LoginCode` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - You are about to alter the column `cnpj` on the `RealEstate` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(14)`.
  - You are about to alter the column `rentAmount` on the `RentalAdvanceRequest` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(12,2)`.
  - You are about to alter the column `monthsToAdvance` on the `RentalAdvanceRequest` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `SmallInt`.
  - You are about to alter the column `proposalAmount` on the `RentalAdvanceRequest` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(12,2)`.
  - You are about to alter the column `monthlyRentOffer` on the `RentalAdvanceRequest` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(12,2)`.
  - You are about to alter the column `proposedMonths` on the `RentalAdvanceRequest` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `SmallInt`.
  - You are about to alter the column `pixKey` on the `RentalAdvanceRequest` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `cpf` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(11)`.
  - You are about to alter the column `phone` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(15)`.
  - Made the column `name` on table `RealEstate` required. This step will fail if there are existing NULL values in that column.
  - Made the column `currentStatus` on table `RentalAdvanceRequest` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `updatedAt` to the `User` table without a default value. This is not possible if the table is not empty.
  - Made the column `name` on table `User` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "LoginCode" ALTER COLUMN "code" SET DATA TYPE VARCHAR(6);

-- AlterTable
ALTER TABLE "RealEstate" ALTER COLUMN "name" SET NOT NULL,
ALTER COLUMN "cnpj" SET DATA TYPE VARCHAR(14);

-- AlterTable
ALTER TABLE "RentalAdvanceRequest" ALTER COLUMN "rentAmount" SET DATA TYPE DECIMAL(12,2),
ALTER COLUMN "monthsToAdvance" SET DATA TYPE SMALLINT,
ALTER COLUMN "currentStatus" SET NOT NULL,
ALTER COLUMN "currentStatus" SET DEFAULT 'created',
ALTER COLUMN "proposalAmount" SET DATA TYPE DECIMAL(12,2),
ALTER COLUMN "monthlyRentOffer" SET DATA TYPE DECIMAL(12,2),
ALTER COLUMN "proposedMonths" SET DATA TYPE SMALLINT,
ALTER COLUMN "pixKey" SET DATA TYPE VARCHAR(255);

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "name" SET NOT NULL,
ALTER COLUMN "cpf" SET DATA TYPE VARCHAR(11),
ALTER COLUMN "phone" SET DATA TYPE VARCHAR(15);

-- CreateIndex
CREATE INDEX "LoginCode_code_expiresAt_idx" ON "LoginCode"("code", "expiresAt");

-- CreateIndex
CREATE INDEX "RealEstate_cnpj_idx" ON "RealEstate"("cnpj");

-- CreateIndex
CREATE INDEX "RentalAdvanceRequest_createdAt_idx" ON "RentalAdvanceRequest"("createdAt");

-- CreateIndex
CREATE INDEX "RentalAdvanceRequest_realEstateId_idx" ON "RentalAdvanceRequest"("realEstateId");

-- CreateIndex
CREATE INDEX "RentalRequestStatusLog_createdAt_idx" ON "RentalRequestStatusLog"("createdAt");

-- CreateIndex
CREATE INDEX "User_cpf_idx" ON "User"("cpf");
