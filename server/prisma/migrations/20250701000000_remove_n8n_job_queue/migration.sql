-- Remove N8nJobQueue table as we now use direct N8N responses instead of Redis jobs

-- Drop all indexes first
DROP INDEX IF EXISTS "N8nJobQueue_jobId_key";
DROP INDEX IF EXISTS "N8nJobQueue_jobId_idx";
DROP INDEX IF EXISTS "N8nJobQueue_status_idx";
DROP INDEX IF EXISTS "N8nJobQueue_type_idx";
DROP INDEX IF EXISTS "N8nJobQueue_relatedEntityId_entityType_idx";
DROP INDEX IF EXISTS "N8nJobQueue_createdAt_idx";

-- Drop the table
DROP TABLE IF EXISTS "N8nJobQueue";
