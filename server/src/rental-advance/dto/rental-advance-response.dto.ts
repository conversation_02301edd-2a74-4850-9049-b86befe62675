export interface RentalAdvanceItemDto {
  id: string;
  rentAmount: number;
  monthsToAdvance: number;
  currentStatus: string;
  statusLabel: string;
  proposalAmount?: number;
  monthlyRentOffer?: number;
  proposedMonths?: number;
  realEstate?: {
    name: string;
    cnpj: string;
  };
  createdAt: string;
  updatedAt: string;
  canEdit: boolean;
  canCancel: boolean;
  nextSteps: string[];
}

export interface RentalAdvanceDetailDto {
  id: string;
  rentAmount: number;
  monthsToAdvance: number;
  currentStatus: string;
  statusLabel: string;
  proposalAmount?: number;
  monthlyRentOffer?: number;
  proposedMonths?: number;
  pixKey?: string;
  realEstate?: {
    id: string;
    name: string;
    cnpj: string;
  };
  contractData?: {
    propertyAddress?: string;
    landlordName?: string;
    tenantName?: string;
    landlordDocument?: string;
    tenantDocument?: string;
    rentalGuarantee?: string;
    contractTerm?: string;
    startDate?: string;
    endDate?: string;
    propertyRegistry?: string;
  };
  documents: {
    contractPdf?: {
      url: string;
      uploadedAt: string;
    };
    identityDoc?: {
      url: string;
      uploadedAt: string;
    };
  };
  statusHistory: {
    status: string;
    statusLabel: string;
    createdAt: string;
  }[];
  createdAt: string;
  updatedAt: string;
  canEdit: boolean;
  canCancel: boolean;
  nextSteps: string[];
  estimatedCompletion?: string;
}

export interface CreateRentalAdvanceResponseDto {
  operationId: string;
  message: string;
  status: string;
  estimatedProcessingTime: string;
  nextSteps: string[];
}

export interface ExtractedDataResponseDto {
  operationId: string;
  extractionStatus: 'processing' | 'completed' | 'failed' | 'pending_review';
  extractedData?: {
    propertyAddress?: string;
    landlordName?: string;
    tenantName?: string;
    landlordDocument?: string;
    tenantDocument?: string;
    rentalGuarantee?: string;
    contractTerm?: string;
    startDate?: string;
    endDate?: string;
    propertyRegistry?: string;
    rentAmount?: number;
    [key: string]: any;
  };
  validationErrors?: {
    field: string;
    message: string;
    severity: 'error' | 'warning';
  }[];
  confidence?: number;
  reviewRequired?: boolean;
  message: string;
}

export interface ProposalResponseDto {
  operationId: string;
  proposal: {
    liquidAmount: number; // Valor líquido que o cliente recebe
    totalAmount: number; // Valor total da operação
    monthlyAmount: number; // Valor mensal descontado
    proposedMonths: number;
    interestRate: number;
    fees: {
      origination: number;
      service: number;
      total: number;
    };
    schedule: {
      month: number;
      discountAmount: number;
      remainingBalance: number;
    }[];
  };
  expiresAt: string;
  termsAndConditions: string;
  nextSteps: string[];
}

export interface FinalConfirmationResponseDto {
  operationId: string;
  status: string;
  message: string;
  estimatedDepositDate?: string;
  trackingId: string;
  nextSteps: string[];
  supportContact: {
    whatsapp: string;
    email: string;
    hours: string;
  };
}
