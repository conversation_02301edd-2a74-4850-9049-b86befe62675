import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { isAdmin: true },
    });

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.name);
      return;
    }

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        name: 'Admin LocPay',
        cpf: '00000000000', // Use a test CPF
        phone: '+5511999999999',
        role: 'admin',
        isAdmin: true,
      },
    });

    console.log('Admin user created successfully:');
    console.log('ID:', adminUser.id);
    console.log('Name:', adminUser.name);
    console.log('CPF:', adminUser.cpf);
    console.log('Role:', adminUser.role);
    console.log('Is Admin:', adminUser.isAdmin);
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
