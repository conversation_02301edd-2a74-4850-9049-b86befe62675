import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

@Injectable()
export class ErrorInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ErrorInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        this.logger.error('Erro capturado:', error);

        // Erros do Prisma
        if (error instanceof PrismaClientKnownRequestError) {
          return throwError(() => this.handlePrismaError(error));
        }

        // Erros HTTP já tratados
        if (error instanceof HttpException) {
          return throwError(() => error);
        }

        // Erro genérico
        return throwError(
          () =>
            new HttpException(
              'Erro interno do servidor. Tente novamente mais tarde.',
              HttpStatus.INTERNAL_SERVER_ERROR,
            ),
        );
      }),
    );
  }

  private handlePrismaError(
    error: PrismaClientKnownRequestError,
  ): HttpException {
    switch (error.code) {
      case 'P2002': {
        const field = (error.meta?.target as string[]) || ['campo'];
        return new HttpException(
          `Dados duplicados: ${field.join(', ')} já existe(m)`,
          HttpStatus.CONFLICT,
        );
      }
      case 'P2025':
        return new HttpException(
          'Registro não encontrado',
          HttpStatus.NOT_FOUND,
        );
      case 'P2003':
        return new HttpException(
          'Violação de constraint de chave estrangeira',
          HttpStatus.BAD_REQUEST,
        );
      case 'P2014':
        return new HttpException(
          'Violação de relacionamento obrigatório',
          HttpStatus.BAD_REQUEST,
        );
      default:
        this.logger.error(`Erro não mapeado do Prisma: ${error.code}`, error);
        return new HttpException(
          'Erro no banco de dados',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
  }
}
