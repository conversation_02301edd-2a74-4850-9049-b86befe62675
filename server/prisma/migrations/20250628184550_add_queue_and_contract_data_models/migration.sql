/*
  Warnings:

  - Added the required column `updatedAt` to the `RentalContractData` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "RentalContractData" ADD COLUMN     "contractTerm" TEXT,
ADD COLUMN     "endDate" TIMESTAMP(3),
ADD COLUMN     "landlordDocument" TEXT,
ADD COLUMN     "landlordName" TEXT,
ADD COLUMN     "propertyAddress" TEXT,
ADD COLUMN     "propertyRegistry" TEXT,
ADD COLUMN     "rentalGuarantee" TEXT,
ADD COLUMN     "startDate" TIMESTAMP(3),
ADD COLUMN     "tenantDocument" TEXT,
ADD COLUMN     "tenantName" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "extractedData" DROP NOT NULL;

-- CreateTable
CREATE TABLE "N8nJobQueue" (
    "id" TEXT NOT NULL,
    "jobId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "requestData" JSONB NOT NULL,
    "responseData" JSONB,
    "relatedEntityId" TEXT,
    "entityType" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "maxRetries" INTEGER NOT NULL DEFAULT 3,
    "errorMessage" TEXT,
    "processedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "N8nJobQueue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "N8nJobQueue_jobId_key" ON "N8nJobQueue"("jobId");

-- CreateIndex
CREATE INDEX "N8nJobQueue_jobId_idx" ON "N8nJobQueue"("jobId");

-- CreateIndex
CREATE INDEX "N8nJobQueue_status_idx" ON "N8nJobQueue"("status");

-- CreateIndex
CREATE INDEX "N8nJobQueue_type_idx" ON "N8nJobQueue"("type");

-- CreateIndex
CREATE INDEX "N8nJobQueue_relatedEntityId_entityType_idx" ON "N8nJobQueue"("relatedEntityId", "entityType");

-- CreateIndex
CREATE INDEX "N8nJobQueue_createdAt_idx" ON "N8nJobQueue"("createdAt");

-- CreateIndex
CREATE INDEX "RentalContractData_landlordDocument_idx" ON "RentalContractData"("landlordDocument");

-- CreateIndex
CREATE INDEX "RentalContractData_tenantDocument_idx" ON "RentalContractData"("tenantDocument");
