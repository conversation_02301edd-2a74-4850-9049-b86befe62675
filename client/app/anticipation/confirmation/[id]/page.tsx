"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useF<PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  CreditCard,
  Upload,
  ChevronLeft,
  X,
  CheckCircle2,
  CalendarDays,
  FileText,
  MapPin,
  DollarSign,
  Calendar,
} from "lucide-react"
import Image from "next/image"

const confirmationSchema = z.object({
  pixKey: z.string().min(1, "Chave PIX é obrigatória"),
  documentFileName: z.string().min(1, "Documento com foto é obrigatório"),
  finalConsent: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
})

interface RentalAdvance {
  id: string
  status: string
  proposal: {
    netAmount: number
    anticipationMonths: number
    monthlyRent: number
  }
  extractedData: {
    Imóvel: string
  }
}

export default function ConfirmationPage({ params }: { params: { id: string } }) {
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [rentalAdvance, setRentalAdvance] = useState<RentalAdvance | null>(null)

  const form = useForm<z.infer<typeof confirmationSchema>>({
    resolver: zodResolver(confirmationSchema),
    defaultValues: {
      pixKey: "",
      documentFileName: "",
      finalConsent: false,
    },
  })

  useEffect(() => {
    const loadRentalAdvance = async () => {
      try {
        const authData = localStorage.getItem("x-auth-state")
        if (!authData) {
          window.location.href = "/"
          return
        }

        const { authToken } = JSON.parse(authData)

        const response = await fetch(`/api/v1/rental-advance/${params.id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        })

        if (response.ok) {
          const data = await response.json()
          setRentalAdvance(data)
        } else {
          setError("Erro ao carregar dados da antecipação")
        }
      } catch (error) {
        console.error("Error loading rental advance:", error)
        setError("Erro de conexão. Tente novamente.")
      } finally {
        setLoading(false)
      }
    }

    loadRentalAdvance()
  }, [params.id])

  const handleDocumentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && (file.type === "application/pdf" || file.type.startsWith("image/"))) {
      form.setValue("documentFileName", file.name)
      setError("")
    } else if (file) {
      setError("Por favor, envie um arquivo PDF ou imagem.")
      form.setValue("documentFileName", "")
    } else {
      form.setValue("documentFileName", "")
    }
  }

  const onSubmit = async (data: z.infer<typeof confirmationSchema>) => {
    setSubmitting(true)
    setError("")
    setSuccess("")

    try {
      const authData = localStorage.getItem("x-auth-state")
      if (!authData) {
        window.location.href = "/"
        return
      }

      const { authToken } = JSON.parse(authData)

      // Create FormData for file upload
      const formData = new FormData()
      const fileInput = document.getElementById("documento") as HTMLInputElement
      const file = fileInput?.files?.[0]

      if (!file) {
        setError("Por favor, selecione um documento.")
        return
      }

      formData.append("document", file)
      formData.append("pixKey", data.pixKey)
      formData.append("rentalAdvanceId", params.id)

      const response = await fetch("/api/v1/rental-advance/final-confirmation", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
        body: formData,
      })

      const result = await response.json()

      if (response.ok) {
        setSuccess("Confirmação enviada! Redirecionando...")
        setTimeout(() => {
          window.location.href = `/anticipation/success/${params.id}`
        }, 1500)
      } else {
        setError(result.message || "Erro ao enviar confirmação")
      }
    } catch (error) {
      console.error("Error submitting confirmation:", error)
      setError("Erro de conexão. Tente novamente.")
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Carregando dados...</p>
        </div>
      </div>
    )
  }

  if (!rentalAdvance) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center">
          <p className="text-white mb-4">Erro ao carregar dados</p>
          <Button
            onClick={() => window.location.href = "/dashboard"}
            className="bg-white text-blue-600 hover:bg-gray-100"
          >
            Voltar ao Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = `/anticipation/proposal/${params.id}`}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Voltar"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Fechar"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="px-4">
        <div className="text-center mb-6">
          <div className="text-4xl mb-4">🏁</div>
          <h1 className="text-xl font-bold text-white mb-2">Informe o seu PIX</h1>
          <p className="text-white/90 text-sm">Confirme os detalhes finais da operação</p>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm mb-4">
            {error}
          </div>
        )}
        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm mb-4">
            {success}
          </div>
        )}

        <div className="space-y-4 mb-6">
          <Card className="bg-white shadow-xl rounded-2xl border-0">
            <CardContent className="p-6">
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <Controller
                  name="pixKey"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <div className="space-y-2">
                      <label htmlFor="pixKey" className="block text-sm font-medium text-gray-700">
                        Sua chave PIX <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          id="pixKey"
                          placeholder="CPF, e-mail, telefone ou chave aleatória"
                          value={field.value}
                          onChange={field.onChange}
                          className={`pl-10 pr-4 ${fieldState.error ? "border-red-500 focus:border-red-500" : ""}`}
                          aria-invalid={!!fieldState.error}
                        />
                      </div>
                      {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                    </div>
                  )}
                />

                <Controller
                  name="documentFileName"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        📄 Documento de Identidade (RG/CNH) <span className="text-red-500">*</span>
                      </label>
                      <label
                        htmlFor="documento"
                        className={`flex flex-col items-center justify-center w-full h-16 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${
                          field.value
                            ? "border-blue-500 bg-blue-50 text-blue-600"
                            : "border-gray-300 bg-gray-50 text-gray-500 hover:border-gray-400"
                        } ${fieldState.error ? "border-red-500" : ""}`}
                      >
                        <Upload className={`w-5 h-5 mb-1 ${field.value ? "text-blue-600" : "text-gray-400"}`} />
                        <span className="text-sm font-medium">{field.value || "Clique para fazer upload"}</span>
                        <span className="text-xs text-gray-400">PDF ou imagem - até 10MB</span>
                      </label>
                      <Input
                        id="documento"
                        type="file"
                        accept=".pdf,image/*"
                        onChange={handleDocumentChange}
                        className="hidden"
                      />
                      {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                    </div>
                  )}
                />

                <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-base font-bold text-blue-600 mb-1">Dia do Repasse</h3>
                      <p className="text-sm text-gray-600">
                        Todo dia <span className="font-bold text-blue-600">5</span> do mês
                      </p>
                    </div>
                    <CalendarDays className="h-6 w-6 text-blue-600" />
                  </div>
                </div>

                <Card className="bg-white shadow-lg rounded-lg border-0 overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-[#0B4375] to-[#0B4375] text-white p-4">
                    <CardTitle className="text-lg font-bold flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Resumo da Operação
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <div className="divide-y divide-gray-100">
                      <div className="p-4 flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <MapPin className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-xs text-gray-500 font-medium">Imóvel</p>
                          <p className="font-semibold text-blue-600 text-sm">
                            {rentalAdvance.extractedData?.Imóvel || "Não informado"}
                          </p>
                        </div>
                      </div>
                      <div className="p-4 flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <DollarSign className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-xs text-gray-500 font-medium">Valor do Aluguel</p>
                          <p className="font-semibold text-blue-600 text-sm">
                            {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(rentalAdvance.proposal.monthlyRent)}
                          </p>
                        </div>
                      </div>
                      <div className="p-4 flex items-center gap-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Calendar className="h-5 w-5 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-xs text-gray-500 font-medium">Meses Antecipados</p>
                          <p className="font-semibold text-blue-600 text-sm">
                            {rentalAdvance.proposal.anticipationMonths} meses
                          </p>
                        </div>
                      </div>
                      <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-gray-600 font-medium text-base">Valor líquido a receber</p>
                            <p className="text-xs text-gray-500 mt-1">Valor que será depositado na sua conta</p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-blue-600 text-2xl">
                              {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(rentalAdvance.proposal.netAmount)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Controller
                  name="finalConsent"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <div className="space-y-2">
                      <div className="flex items-start space-x-3 p-3 bg-white border border-gray-200 rounded-lg">
                        <input
                          type="checkbox"
                          id="finalConsent"
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                          className="mt-0.5 w-4 h-4"
                        />
                        <label htmlFor="finalConsent" className="text-sm text-gray-700 cursor-pointer">
                          Confirmo que todas as informações fornecidas são verdadeiras e autorizo a LocPay a processar
                          minha antecipação de aluguel conforme os termos apresentados.
                        </label>
                      </div>
                      {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                    </div>
                  )}
                />

                <Button 
                  type="submit" 
                  className="w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
                  disabled={submitting}
                >
                  {submitting ? "FINALIZANDO..." : "FINALIZAR ANTECIPAÇÃO"}
                  <CheckCircle2 className="w-4 h-4" />
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
