"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { api } from "@/lib/api-handler"
import {
  ArrowLeft,
  User,
  Phone,
  Calendar,
  FileText,
  DollarSign,
  Building,
  RefreshCw,
  AlertCircle,
  Eye,
} from "lucide-react"

interface UserDetails {
  user: {
    id: string
    name: string
    cpf: string
    phone?: string
    role: string
    isAdmin: boolean
    createdAt: string
    totalRequests: number
  }
  recentRequests: Array<{
    id: string
    currentStatus: string
    rentAmount: number
    proposalAmount?: number
    createdAt: string
    realEstate?: {
      name: string
    }
  }>
  statistics: Array<{
    status: string
    count: number
    totalValue: number
  }>
}

export default function UserDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const userId = params.userId as string
  
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  const fetchUserDetails = async () => {
    setLoading(true)
    setError("")

    try {
      const response = await api.adminGet<UserDetails>(`admin/users/${userId}`)
      
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch user details")
      }

      setUserDetails(response.data!)
    } catch (error) {
      console.error("Error fetching user details:", error)
      setError("Erro ao carregar detalhes do usuário")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (userId) {
      fetchUserDetails()
    }
  }, [userId])

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  const formatPhone = (phone?: string) => {
    if (!phone) return "N/A"
    return phone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3")
  }

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      created: "Criada",
      pdf_uploaded: "PDF Enviado",
      pdf_extracted: "Dados Extraídos",
      data_confirmed: "Dados Confirmados",
      pending_proposal: "Aguardando Proposta",
      proposal_sent: "Proposta Enviada",
      docs_uploaded: "Documentos Enviados",
      awaiting_review: "Em Análise",
      approved: "Aprovada",
      rejected: "Rejeitada",
      cancelled: "Cancelada",
    }
    return labels[status] || status
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { variant: "default" | "secondary" | "destructive" | "outline" }> = {
      approved: { variant: "default" },
      awaiting_review: { variant: "secondary" },
      rejected: { variant: "destructive" },
      cancelled: { variant: "outline" },
    }
    
    const config = statusMap[status] || { variant: "outline" as const }
    return <Badge variant={config.variant}>{getStatusLabel(status)}</Badge>
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <h2 className="text-2xl font-bold text-gray-900">Carregando...</h2>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <h2 className="text-2xl font-bold text-gray-900">Erro</h2>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
            <Button onClick={fetchUserDetails} className="mt-4">
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!userDetails) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{userDetails.user.name}</h2>
            <p className="text-gray-600">Detalhes do usuário</p>
          </div>
        </div>
        <Button onClick={fetchUserDetails}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Informações Pessoais
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Nome Completo</label>
              <p className="text-lg font-semibold">{userDetails.user.name}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600">CPF</label>
              <p className="text-lg font-semibold">{formatCPF(userDetails.user.cpf)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600">Telefone</label>
              <p className="text-lg font-semibold">{formatPhone(userDetails.user.phone)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600">Tipo de Usuário</label>
              <div className="flex items-center gap-2">
                <p className="text-lg font-semibold">{userDetails.user.role}</p>
                {userDetails.user.isAdmin && <Badge>Admin</Badge>}
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600">Cadastrado em</label>
              <p className="text-lg font-semibold">{formatDate(userDetails.user.createdAt)}</p>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Estatísticas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{userDetails.user.totalRequests}</p>
              <p className="text-sm text-blue-600">Total de Solicitações</p>
            </div>
            
            {userDetails.statistics.map((stat) => (
              <div key={stat.status} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{getStatusLabel(stat.status)}</p>
                  <p className="text-sm text-gray-600">{formatCurrency(stat.totalValue)}</p>
                </div>
                <Badge variant="outline">{stat.count}</Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Recent Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Solicitações Recentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            {userDetails.recentRequests.length === 0 ? (
              <p className="text-gray-600 text-center py-4">Nenhuma solicitação encontrada</p>
            ) : (
              <div className="space-y-3">
                {userDetails.recentRequests.map((request) => (
                  <div key={request.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline">ID: {request.id.slice(-8)}</Badge>
                          {getStatusBadge(request.currentStatus)}
                        </div>
                        
                        <div className="text-sm space-y-1">
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-3 w-3 text-gray-400" />
                            <span>Aluguel: {formatCurrency(request.rentAmount)}</span>
                          </div>
                          
                          {request.proposalAmount && (
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-3 w-3 text-green-600" />
                              <span>Proposta: {formatCurrency(request.proposalAmount)}</span>
                            </div>
                          )}
                          
                          {request.realEstate && (
                            <div className="flex items-center gap-2">
                              <Building className="h-3 w-3 text-gray-400" />
                              <span>{request.realEstate.name}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-3 w-3 text-gray-400" />
                            <span>{formatDate(request.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <Button variant="ghost" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
