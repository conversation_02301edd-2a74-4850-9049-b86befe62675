"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { api } from "@/lib/api-handler"
import {
  Search,
  Building,
  Plus,
  RefreshCw,
  AlertCircle,
  Eye,
  Trash2,
  Calendar,
  FileText,
} from "lucide-react"
import CreateRealEstateModal from "@/components/admin/CreateRealEstateModal"
import RemoveRealEstateModal from "@/components/admin/RemoveRealEstateModal"

interface RealEstate {
  id: string
  name: string
  cnpj: string
  createdAt: string
  requestCount: number
}

interface RealEstatesResponse {
  realEstates: RealEstate[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function RealEstatesPage() {
  const [realEstates, setRealEstates] = useState<RealEstatesResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState("")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showRemoveModal, setShowRemoveModal] = useState(false)
  const [selectedRealEstate, setSelectedRealEstate] = useState<RealEstate | null>(null)

  const fetchRealEstates = async (page: number = 1, search: string = "") => {
    setLoading(true)
    setError("")

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
      })

      if (search.trim()) {
        params.append("search", search.trim())
      }

      const response = await api.adminGet<RealEstatesResponse>(`admin/real-estates/search?${params}`)
      
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch real estates")
      }

      setRealEstates(response.data!)
      setCurrentPage(page)
    } catch (error) {
      console.error("Error fetching real estates:", error)
      setError("Erro ao carregar imobiliárias")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRealEstates()
  }, [])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchRealEstates(1, searchTerm)
  }

  const handleClearSearch = () => {
    setSearchTerm("")
    setCurrentPage(1)
    fetchRealEstates(1, "")
  }

  const formatCNPJ = (cnpj: string) => {
    return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const handleViewDetails = (realEstateId: string) => {
    window.location.href = `/admin/real-estates/${realEstateId}`
  }

  const handleRemove = (realEstate: RealEstate) => {
    setSelectedRealEstate(realEstate)
    setShowRemoveModal(true)
  }

  const handleCreateSuccess = () => {
    fetchRealEstates(1, searchTerm)
    setCurrentPage(1)
  }

  if (loading && !realEstates) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold text-primary">Imobiliárias Parceiras</h2>
          <Button disabled className="bg-primary/50">
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            Carregando...
          </Button>
        </div>
        <div className="grid grid-cols-1 gap-6">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse shadow-lg">
              <CardContent className="p-6">
                <div className="h-4 bg-primary/20 rounded mb-2"></div>
                <div className="h-6 bg-primary/30 rounded mb-4"></div>
                <div className="flex gap-2">
                  <div className="h-8 bg-primary/20 rounded w-20"></div>
                  <div className="h-8 bg-primary/20 rounded w-24"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold text-primary">Imobiliárias Parceiras</h2>
          <Button onClick={() => fetchRealEstates(currentPage, searchTerm)} className="bg-primary hover:bg-primary/90">
            <RefreshCw className="mr-2 h-4 w-4" />
            Tentar Novamente
          </Button>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <p>{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-primary">Imobiliárias Parceiras</h2>
          <p className="text-muted-foreground">
            {realEstates?.pagination.total || 0} imobiliária{realEstates?.pagination.total !== 1 ? "s" : ""} cadastrada{realEstates?.pagination.total !== 1 ? "s" : ""}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => fetchRealEstates(currentPage, searchTerm)} variant="outline" className="border-primary text-primary hover:bg-primary/10">
            <RefreshCw className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
          <Button onClick={() => setShowCreateModal(true)} className="bg-primary hover:bg-primary/90">
            <Plus className="mr-2 h-4 w-4" />
            Nova Imobiliária
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-primary">
            <Search className="h-5 w-5" />
            Buscar Imobiliárias
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Buscar por nome ou CNPJ..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} className="bg-primary hover:bg-primary/90">
              <Search className="mr-2 h-4 w-4" />
              Buscar
            </Button>
            <Button onClick={handleClearSearch} variant="outline" className="border-primary text-primary hover:bg-primary/10">
              Limpar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Real Estates List */}
      {realEstates && (
        <>
          {realEstates.realEstates.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhuma imobiliária encontrada</h3>
                <p className="text-gray-600">Tente ajustar os filtros ou cadastrar uma nova imobiliária.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {realEstates.realEstates.map((realEstate) => (
                <Card key={realEstate.id} className="hover:shadow-xl transition-shadow duration-200 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-primary/10 rounded-full">
                            <Building className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-primary">{realEstate.name}</h3>
                            <p className="text-sm text-muted-foreground">CNPJ: {formatCNPJ(realEstate.cnpj)}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 mt-4">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            Cadastrada em {formatDate(realEstate.createdAt)}
                          </div>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-primary" />
                            <Badge variant="secondary" className="bg-primary/10 text-primary">
                              {realEstate.requestCount} solicitaç{realEstate.requestCount !== 1 ? "ões" : "ão"}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleViewDetails(realEstate.id)}
                          size="sm"
                          className="bg-primary hover:bg-primary/90"
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Ver Detalhes
                        </Button>
                        <Button
                          onClick={() => handleRemove(realEstate)}
                          size="sm"
                          variant="outline"
                          className="border-red-300 text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Remover
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {realEstates.pagination.totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                onClick={() => fetchRealEstates(currentPage - 1, searchTerm)}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                Anterior
              </Button>
              <span className="text-sm text-muted-foreground">
                Página {currentPage} de {realEstates.pagination.totalPages}
              </span>
              <Button
                onClick={() => fetchRealEstates(currentPage + 1, searchTerm)}
                disabled={currentPage === realEstates.pagination.totalPages}
                variant="outline"
                size="sm"
              >
                Próxima
              </Button>
            </div>
          )}
        </>
      )}

      {/* Create Real Estate Modal */}
      <CreateRealEstateModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {/* Remove Real Estate Modal */}
      <RemoveRealEstateModal
        open={showRemoveModal}
        onOpenChange={setShowRemoveModal}
        realEstateName={selectedRealEstate?.name || ""}
      />
    </div>
  )
}
