import { BadRequestException } from '@nestjs/common';

export class ValidationUtils {
  static validateCPF(cpf: string): boolean {
    if (!cpf || cpf.length !== 11) return false;

    // Remove caracteres especiais
    const cleanCpf = cpf.replace(/[^\d]/g, '');
    if (cleanCpf.length !== 11) return false;

    // Verifica se todos os dígitos são iguais
    if (/^(\d)\1{10}$/.test(cleanCpf)) return false;

    // Validação dos dígitos verificadores
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCpf[i]) * (10 - i);
    }
    let remainder = sum % 11;
    const firstDigit = remainder < 2 ? 0 : 11 - remainder;

    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCpf[i]) * (11 - i);
    }
    remainder = sum % 11;
    const secondDigit = remainder < 2 ? 0 : 11 - remainder;

    return (
      parseInt(cleanCpf[9]) === firstDigit &&
      parseInt(cleanCpf[10]) === secondDigit
    );
  }

  static validateCNPJ(cnpj: string): boolean {
    if (!cnpj || cnpj.length !== 14) return false;

    const cleanCnpj = cnpj.replace(/[^\d]/g, '');
    if (cleanCnpj.length !== 14) return false;

    // Verifica se todos os dígitos são iguais
    if (/^(\d)\1{13}$/.test(cleanCnpj)) return false;

    // Validação dos dígitos verificadores
    const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

    let sum = 0;
    for (let i = 0; i < 12; i++) {
      sum += parseInt(cleanCnpj[i]) * weights1[i];
    }
    let remainder = sum % 11;
    const firstDigit = remainder < 2 ? 0 : 11 - remainder;

    sum = 0;
    for (let i = 0; i < 13; i++) {
      sum += parseInt(cleanCnpj[i]) * weights2[i];
    }
    remainder = sum % 11;
    const secondDigit = remainder < 2 ? 0 : 11 - remainder;

    return (
      parseInt(cleanCnpj[12]) === firstDigit &&
      parseInt(cleanCnpj[13]) === secondDigit
    );
  }

  static validatePhone(phone: string): boolean {
    if (!phone) return false;
    const cleanPhone = phone.replace(/[^\d]/g, '');
    return cleanPhone.length >= 10 && cleanPhone.length <= 11;
  }

  static validateEmail(email: string): boolean {
    if (!email) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePixKey(pixKey: string, type: string): boolean {
    switch (type.toLowerCase()) {
      case 'cpf':
        return this.validateCPF(pixKey);
      case 'email':
        return this.validateEmail(pixKey);
      case 'phone':
        return this.validatePhone(pixKey);
      case 'random':
        return pixKey.length >= 32; // Chave aleatória tem pelo menos 32 caracteres
      default:
        return false;
    }
  }

  static generateRandomCode(length: number = 6): string {
    const digits = '0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
      code += digits[Math.floor(Math.random() * digits.length)];
    }
    return code;
  }

  static sanitizeCPF(cpf: string): string {
    return cpf.replace(/[^\d]/g, '');
  }

  static sanitizeCNPJ(cnpj: string): string {
    return cnpj.replace(/[^\d]/g, '');
  }

  static sanitizePhone(phone: string): string {
    return phone.replace(/[^\d]/g, '');
  }

  static validateFileType(
    file: Express.Multer.File,
    allowedTypes: string[],
  ): void {
    if (!allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Tipo de arquivo não permitido. Permitidos: ${allowedTypes.join(', ')}`,
      );
    }
  }

  static validateFileSize(
    file: Express.Multer.File,
    maxSizeInMB: number,
  ): void {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      throw new BadRequestException(
        `Arquivo muito grande. Tamanho máximo: ${maxSizeInMB}MB`,
      );
    }
  }

  static validateCuid(cuid: string): boolean {
    if (!cuid) return false;

    // CUID format: c[timestamp][counter][fingerprint][random]
    // Example: cjld2cjxh0000qzrmn831i7rn
    // - Starts with 'c'
    // - Length is typically 25 characters
    // - Contains only lowercase letters and numbers
    const cuidRegex = /^c[0-9a-z]{24}$/;
    return cuidRegex.test(cuid);
  }
}
