"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { MessageCircle, X, Send, User, Minimize2, <PERSON><PERSON><PERSON>, Zap } from "lucide-react"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface ConversationMessage {
  role: "user" | "assistant"
  content: string
}

export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [shouldHide, setShouldHide] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen, isMinimized])

  // Prevenir scroll do body quando o chat estiver aberto
  useEffect(() => {
    if (isOpen && !isMinimized && typeof document !== "undefined") {
      document.body.style.overflow = "hidden"
      return () => {
        document.body.style.overflow = "unset"
      }
    }
  }, [isOpen, isMinimized])

  // Monitorar mudanças nas flags globais e forçar re-renderização
  useEffect(() => {
    const checkFormsStatus = () => {
      if (typeof window === "undefined") return

      const isMobile = window.innerWidth < 768
      const hasFormsOpen = window.locpayFormOpen || window.locpayPopupOpen
      const isAvaliacaoPage = window.location.pathname === "/avaliacao"

      if ((isMobile && hasFormsOpen) || isAvaliacaoPage) {
        setShouldHide(true)
        // Se o chat estiver aberto, feche-o
        if (isOpen) {
          setIsOpen(false)
        }
      } else {
        setShouldHide(false)
      }
    }

    // Verificar imediatamente
    checkFormsStatus()

    // Verificar a cada 100ms para detectar mudanças nas flags
    const interval = setInterval(checkFormsStatus, 100)

    // Verificar quando a janela redimensiona
    const handleResize = () => {
      checkFormsStatus()
    }

    // Verificar quando a URL muda
    const handlePopState = () => {
      checkFormsStatus()
    }

    window.addEventListener("resize", handleResize)
    window.addEventListener("popstate", handlePopState)

    return () => {
      clearInterval(interval)
      window.removeEventListener("resize", handleResize)
      window.removeEventListener("popstate", handlePopState)
    }
  }, [isOpen])

  const handleOpenChat = () => {
    // Verificar se pode abrir o chat
    if (typeof window !== "undefined") {
      const isMobile = window.innerWidth < 768
      const hasFormsOpen = window.locpayFormOpen || window.locpayPopupOpen
      const isAvaliacaoPage = window.location.pathname === "/avaliacao"

      if ((isMobile && hasFormsOpen) || isAvaliacaoPage) {
        return // Não abrir o chat se há formulários abertos no mobile ou se está na página de avaliação
      }
    }

    setIsOpen(true)
    if (messages.length === 0) {
      // Mensagem inicial da Lia - mais curta e comercial
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        content: "Oi! Sou a Lia da LocPay! 💰 Antecipe até 12 meses de aluguel em 12h. Como posso te ajudar?",
        role: "assistant",
        timestamp: new Date(),
      }
      setMessages([welcomeMessage])
    }
  }

  const sendMessage = async (): Promise<void> => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      role: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputMessage("")
    setIsLoading(true)

    try {
      // Preparar histórico da conversa para a API
      const conversationHistory: ConversationMessage[] = messages.map((msg) => ({
        role: msg.role === "user" ? "user" : "assistant",
        content: msg.content,
      }))

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: inputMessage,
          conversationHistory,
        }),
      })

      const data = await response.json()

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.response,
        role: "assistant",
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, botMessage])
    } catch (error) {
      console.error("Erro ao enviar mensagem:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "Fale direto comigo no WhatsApp! 📱 (85) 99199-2305 - Tenho todas as informações para você! 🚀",
        role: "assistant",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Se deve ocultar o chat, retorna null completamente
  if (shouldHide) {
    return null
  }

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50 md:bottom-6 md:right-6">
        <Button
          onClick={handleOpenChat}
          className="relative bg-gradient-to-r from-[#004B87] via-[#00A7E1] to-[#6BBAED] hover:from-[#003d73] hover:via-[#0090c0] hover:to-[#5aa8db] text-white rounded-full shadow-2xl transition-all duration-500 hover:scale-105 group overflow-hidden min-w-[140px] h-14 px-4"
        >
          {/* Efeito de brilho animado */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

          <div className="relative flex items-center justify-center gap-3 w-full">
            <div className="relative flex-shrink-0">
              <MessageCircle className="w-6 h-6 group-hover:animate-pulse" />
              <Sparkles className="absolute -top-1 -right-1 w-4 h-4 text-yellow-300 animate-pulse" />
            </div>
            <div className="flex flex-col items-start justify-center min-w-0">
              <div className="text-base font-bold leading-tight whitespace-nowrap">Lia</div>
              <div className="text-xs opacity-90 leading-tight whitespace-nowrap">Assistente IA</div>
            </div>
          </div>
          <span className="sr-only">Abrir chat com Lia</span>
        </Button>

        {/* Tooltip moderno - apenas desktop */}
        <div className="hidden md:block absolute bottom-full right-0 mb-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
          <div className="bg-gray-900 text-white text-sm px-5 py-3 rounded-xl shadow-xl relative">
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              <span className="font-medium">Oi! Sou a Lia, sua assistente IA da LocPay!</span>
            </div>
            <div className="absolute top-full right-6 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 z-50 md:inset-auto md:bottom-6 md:right-6">
      {/* Overlay para mobile */}
      <div className="absolute inset-0 bg-black/50 md:hidden" onClick={() => setIsOpen(false)} />

      <div
        ref={chatContainerRef}
        className={`absolute inset-x-4 bottom-4 top-4 md:relative md:inset-auto bg-white rounded-2xl shadow-2xl border border-gray-200 transition-all duration-500 ${
          isMinimized ? "md:w-80 md:h-16" : "md:w-80 md:h-[500px]"
        } backdrop-blur-sm overflow-hidden flex flex-col`}
        style={{
          background: "linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)",
          maxHeight: "calc(100vh - 32px)",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header moderno */}
        <div className="bg-gradient-to-r from-[#004B87] via-[#00A7E1] to-[#6BBAED] text-white p-3 md:p-4 rounded-t-2xl flex items-center justify-between relative overflow-hidden">
          {/* Efeito de ondas no header */}
          <div className="absolute inset-0 opacity-20">
            <svg className="w-full h-full" viewBox="0 0 400 100" preserveAspectRatio="none">
              <path d="M0,50 Q100,20 200,50 T400,50 L400,100 L0,100 Z" fill="rgba(255,255,255,0.1)" />
            </svg>
          </div>

          <div className="flex items-center gap-3 relative z-10">
            <div className="relative">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30">
                <div className="w-4 h-4 md:w-6 md:h-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-2 h-2 md:w-3 md:h-3 text-white" />
                </div>
              </div>
              <div className="absolute -bottom-1 -right-1 w-3 h-3 md:w-4 md:h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
            </div>
            <div>
              <div className="font-bold text-base md:text-lg flex items-center gap-2">
                Lia
                <Zap className="w-3 h-3 md:w-4 md:h-4 text-yellow-300 animate-pulse" />
              </div>
              <div className="text-xs text-white/80 flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                Assistente IA • Online
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1 relative z-10">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMinimized(!isMinimized)}
              className="hidden md:flex text-white hover:bg-white/20 h-8 w-8 rounded-full transition-all duration-300 hover:scale-110"
            >
              <Minimize2 className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
              className="text-white hover:bg-white/20 h-8 w-8 rounded-full transition-all duration-300 hover:scale-110"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages com scroll isolado */}
            <div
              className="flex-1 p-3 md:p-4 overflow-y-auto space-y-3 md:space-y-4 bg-gradient-to-b from-gray-50/50 to-white"
              style={{
                height: "calc(100vh - 280px)",
                minHeight: "300px",
                maxHeight: "calc(100vh - 280px)",
                WebkitOverflowScrolling: "touch",
                overscrollBehavior: "contain",
              }}
              onTouchMove={(e) => e.stopPropagation()}
            >
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex gap-2 md:gap-3 ${message.role === "user" ? "justify-end" : "justify-start"} animate-in slide-in-from-bottom-2 duration-300`}
                >
                  {message.role === "assistant" && (
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#00A7E1] to-[#6BBAED] rounded-full flex items-center justify-center flex-shrink-0 mt-1 shadow-lg">
                      <Sparkles className="w-3 h-3 md:w-4 md:h-4 text-white" />
                    </div>
                  )}
                  <div
                    className={`max-w-[80%] md:max-w-[75%] p-3 md:p-4 rounded-2xl text-sm shadow-lg transition-all duration-300 hover:shadow-xl ${
                      message.role === "user"
                        ? "bg-gradient-to-r from-[#00A7E1] to-[#6BBAED] text-white rounded-br-md"
                        : "bg-white text-gray-800 rounded-bl-md border border-gray-100"
                    }`}
                  >
                    <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                    <span
                      className={`text-xs mt-2 block ${message.role === "user" ? "text-white/70" : "text-gray-500"}`}
                    >
                      {formatTime(message.timestamp)}
                    </span>
                  </div>
                  {message.role === "user" && (
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1 shadow-lg">
                      <User className="w-3 h-3 md:w-4 md:h-4 text-white" />
                    </div>
                  )}
                </div>
              ))}

              {isLoading && (
                <div className="flex gap-2 md:gap-3 justify-start animate-in slide-in-from-bottom-2 duration-300">
                  <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#00A7E1] to-[#6BBAED] rounded-full flex items-center justify-center flex-shrink-0 mt-1 shadow-lg">
                    <Sparkles className="w-3 h-3 md:w-4 md:h-4 text-white animate-pulse" />
                  </div>
                  <div className="bg-white p-3 md:p-4 rounded-2xl rounded-bl-md shadow-lg border border-gray-100">
                    <div className="flex space-x-2">
                      <div className="w-2 h-2 bg-gradient-to-r from-[#00A7E1] to-[#6BBAED] rounded-full animate-bounce"></div>
                      <div
                        className="w-2 h-2 bg-gradient-to-r from-[#00A7E1] to-[#6BBAED] rounded-full animate-bounce"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-gradient-to-r from-[#00A7E1] to-[#6BBAED] rounded-full animate-bounce"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>

            {/* Input moderno */}
            <div className="p-3 md:p-4 border-t border-gray-200 bg-white rounded-b-2xl">
              <div className="flex gap-2 md:gap-3 items-end">
                <div className="flex-1 relative">
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder="Mensagem para Lia..."
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    disabled={isLoading}
                    className="pr-10 py-2 md:py-3 rounded-xl border-2 border-gray-200 focus:border-[#00A7E1] focus:ring-2 focus:ring-[#00A7E1]/20 transition-all duration-300 bg-gray-50 focus:bg-white text-sm placeholder:text-gray-400"
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Sparkles className="w-4 h-4 text-gray-400" />
                  </div>
                </div>
                <Button
                  onClick={sendMessage}
                  disabled={isLoading || !inputMessage.trim()}
                  className="bg-gradient-to-r from-[#00A7E1] to-[#6BBAED] hover:from-[#0090c0] hover:to-[#5aa8db] text-white px-3 py-2 md:px-4 md:py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4 md:w-5 md:h-5" />
                </Button>
              </div>
              <div className="flex items-center justify-center mt-2 md:mt-3 gap-2">
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Zap className="w-3 h-3 text-[#00A7E1]" />
                  <span>Powered by Lia AI</span>
                </div>
                <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                <span className="text-xs text-gray-500">(85) 99199-2305</span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
