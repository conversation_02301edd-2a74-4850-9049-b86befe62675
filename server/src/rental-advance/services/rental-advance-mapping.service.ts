import { Injectable } from '@nestjs/common';
import { 
  RentalAdvanceItemDto, 
  RentalAdvanceDetailDto,
  ExtractedDataResponseDto,
  ProposalResponseDto,
  FinalConfirmationResponseDto,
  CreateRentalAdvanceResponseDto
} from '../dto/rental-advance-response.dto';
import { RentalAdvanceStatus } from '../enums/rental-status.enum';

@Injectable()
export class RentalAdvanceMappingService {
  
  /**
   * Converte dados do Prisma para DTO de item da lista
   */
  toRentalAdvanceItem(data: any): RentalAdvanceItemDto {
    return {
      id: data.id,
      rentAmount: Number(data.rentAmount),
      monthsToAdvance: data.monthsToAdvance,
      currentStatus: data.currentStatus,
      statusLabel: this.getStatusLabel(data.currentStatus),
      proposalAmount: data.proposalAmount ? Number(data.proposalAmount) : undefined,
      monthlyRentOffer: data.monthlyRentOffer ? Number(data.monthlyRentOffer) : undefined,
      proposedMonths: data.proposedMonths,
      realEstate: data.realEstate ? {
        name: data.realEstate.name,
        cnpj: data.realEstate.cnpj,
      } : undefined,
      createdAt: data.createdAt.toISOString(),
      updatedAt: data.updatedAt.toISOString(),
      canEdit: this.canEdit(data.currentStatus),
      canCancel: this.canCancel(data.currentStatus),
      nextSteps: this.getNextSteps(data.currentStatus),
    };
  }

  /**
   * Converte dados do Prisma para DTO detalhado
   */
  toRentalAdvanceDetail(data: any): RentalAdvanceDetailDto {
    return {
      id: data.id,
      rentAmount: Number(data.rentAmount),
      monthsToAdvance: data.monthsToAdvance,
      currentStatus: data.currentStatus,
      statusLabel: this.getStatusLabel(data.currentStatus),
      proposalAmount: data.proposalAmount ? Number(data.proposalAmount) : undefined,
      monthlyRentOffer: data.monthlyRentOffer ? Number(data.monthlyRentOffer) : undefined,
      proposedMonths: data.proposedMonths,
      pixKey: data.pixKey,
      realEstate: data.realEstate ? {
        id: data.realEstate.id,
        name: data.realEstate.name,
        cnpj: data.realEstate.cnpj,
      } : undefined,
      contractData: data.contractData ? {
        propertyAddress: data.contractData.propertyAddress,
        landlordName: data.contractData.landlordName,
        tenantName: data.contractData.tenantName,
        landlordDocument: data.contractData.landlordDocument,
        tenantDocument: data.contractData.tenantDocument,
        rentalGuarantee: data.contractData.rentalGuarantee,
        contractTerm: data.contractData.contractTerm,
        startDate: data.contractData.startDate?.toISOString(),
        endDate: data.contractData.endDate?.toISOString(),
        propertyRegistry: data.contractData.propertyRegistry,
      } : undefined,
      documents: {
        contractPdf: data.contractPdfUrl ? {
          url: data.contractPdfUrl,
          uploadedAt: data.createdAt.toISOString(),
        } : undefined,
        identityDoc: data.identityDocUrl ? {
          url: data.identityDocUrl,
          uploadedAt: data.updatedAt.toISOString(),
        } : undefined,
      },
      statusHistory: (data.statusLogs || []).map((log: any) => ({
        status: log.status,
        statusLabel: this.getStatusLabel(log.status),
        createdAt: log.createdAt.toISOString(),
      })),
      createdAt: data.createdAt.toISOString(),
      updatedAt: data.updatedAt.toISOString(),
      canEdit: this.canEdit(data.currentStatus),
      canCancel: this.canCancel(data.currentStatus),
      nextSteps: this.getNextSteps(data.currentStatus),
      estimatedCompletion: this.getEstimatedCompletion(data.currentStatus),
    };
  }

  /**
   * Cria resposta para criação de solicitação
   */
  toCreateResponse(operationId: string): CreateRentalAdvanceResponseDto {
    return {
      operationId,
      message: 'Solicitação criada com sucesso. Processando análise do contrato...',
      status: RentalAdvanceStatus.PDF_UPLOADED,
      estimatedProcessingTime: '2-5 minutos',
      nextSteps: [
        'Aguarde a análise automática do contrato',
        'Você será notificado quando os dados estiverem prontos para validação',
      ],
    };
  }

  /**
   * Cria resposta para dados extraídos
   */
  toExtractedDataResponse(
    operationId: string,
    extractionStatus: 'processing' | 'completed' | 'failed' | 'pending_review',
    extractedData?: any,
    validationErrors?: any[],
  ): ExtractedDataResponseDto {
    return {
      operationId,
      extractionStatus,
      extractedData,
      validationErrors,
      confidence: extractedData?.confidence || undefined,
      reviewRequired: extractionStatus === 'pending_review',
      message: this.getExtractionMessage(extractionStatus),
    };
  }

  /**
   * Retorna label amigável para status
   */
  private getStatusLabel(status: string): string {
    const statusLabels = {
      [RentalAdvanceStatus.CREATED]: 'Criada',
      [RentalAdvanceStatus.PDF_UPLOADED]: 'Analisando Contrato',
      [RentalAdvanceStatus.PDF_EXTRACTED]: 'Dados Extraídos',
      [RentalAdvanceStatus.DATA_CONFIRMED]: 'Dados Confirmados',
      [RentalAdvanceStatus.PENDING_PROPOSAL]: 'Gerando Proposta',
      [RentalAdvanceStatus.PROPOSAL_SENT]: 'Proposta Gerada',
      [RentalAdvanceStatus.DOCS_UPLOADED]: 'Documentos Enviados',
      [RentalAdvanceStatus.AWAITING_REVIEW]: 'Em Análise',
      [RentalAdvanceStatus.APPROVED]: 'Aprovada',
      [RentalAdvanceStatus.REJECTED]: 'Rejeitada',
      [RentalAdvanceStatus.CANCELLED]: 'Cancelada',
    };
    
    return statusLabels[status] || status;
  }

  /**
   * Verifica se pode editar
   */
  private canEdit(status: string): boolean {
    const editableStatuses = [
      RentalAdvanceStatus.CREATED,
      RentalAdvanceStatus.PDF_EXTRACTED,
    ];
    return editableStatuses.includes(status as RentalAdvanceStatus);
  }

  /**
   * Verifica se pode cancelar
   */
  private canCancel(status: string): boolean {
    const cancellableStatuses = [
      RentalAdvanceStatus.CREATED,
      RentalAdvanceStatus.PDF_UPLOADED,
      RentalAdvanceStatus.PDF_EXTRACTED,
      RentalAdvanceStatus.DATA_CONFIRMED,
      RentalAdvanceStatus.PENDING_PROPOSAL,
      RentalAdvanceStatus.PROPOSAL_SENT,
    ];
    return cancellableStatuses.includes(status as RentalAdvanceStatus);
  }

  /**
   * Retorna próximos passos baseado no status
   */
  private getNextSteps(status: string): string[] {
    const nextStepsMap = {
      [RentalAdvanceStatus.CREATED]: [
        'Aguarde o upload do contrato ser processado',
      ],
      [RentalAdvanceStatus.PDF_UPLOADED]: [
        'Aguarde a análise automática do contrato',
        'Você será notificado quando estiver pronto',
      ],
      [RentalAdvanceStatus.PDF_EXTRACTED]: [
        'Revise os dados extraídos do contrato',
        'Confirme ou corrija as informações',
        'Clique em "Confirmar Dados" para prosseguir',
      ],
      [RentalAdvanceStatus.DATA_CONFIRMED]: [
        'Solicite a geração da proposta de crédito',
      ],
      [RentalAdvanceStatus.PENDING_PROPOSAL]: [
        'Aguarde a geração da proposta',
        'Isso pode levar alguns minutos',
      ],
      [RentalAdvanceStatus.PROPOSAL_SENT]: [
        'Revise a proposta de crédito',
        'Aceite ou recuse os termos',
      ],
      [RentalAdvanceStatus.DOCS_UPLOADED]: [
        'Aguarde análise da nossa equipe',
        'Você será notificado do resultado',
      ],
      [RentalAdvanceStatus.AWAITING_REVIEW]: [
        'Sua solicitação está em análise',
        'Aguarde o resultado da análise',
      ],
      [RentalAdvanceStatus.APPROVED]: [
        'Parabéns! Sua solicitação foi aprovada',
        'O valor será depositado em breve',
      ],
    };

    return nextStepsMap[status] || ['Aguarde atualizações'];
  }

  /**
   * Retorna estimativa de conclusão
   */
  private getEstimatedCompletion(status: string): string | undefined {
    const estimationMap = {
      [RentalAdvanceStatus.PDF_UPLOADED]: '5 minutos',
      [RentalAdvanceStatus.PENDING_PROPOSAL]: '10 minutos',
      [RentalAdvanceStatus.AWAITING_REVIEW]: '24-48 horas',
    };

    return estimationMap[status];
  }

  /**
   * Retorna mensagem para status de extração
   */
  private getExtractionMessage(status: 'processing' | 'completed' | 'failed' | 'pending_review'): string {
    const messages = {
      processing: 'Analisando contrato... Aguarde alguns minutos.',
      completed: 'Dados extraídos com sucesso. Revise as informações abaixo.',
      failed: 'Não foi possível extrair dados do contrato. Verifique se o arquivo está legível.',
      pending_review: 'Extração concluída, mas alguns dados precisam de revisão manual.',
    };

    return messages[status];
  }
}
