"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  CheckCircle,
  CheckCircle2,
  Clock,
  MessageCircle,
  Shield,
  Award,
  Star,
  Plus,
  <PERSON>rkles,
} from "lucide-react"
import Image from "next/image"

interface RentalAdvance {
  id: string
  status: string
  proposal: {
    netAmount: number
  }
}

export default function SuccessPage({ params }: { params: { id: string } }) {
  const [loading, setLoading] = useState(true)
  const [rentalAdvance, setRentalAdvance] = useState<RentalAdvance | null>(null)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const loadData = async () => {
      try {
        const authData = localStorage.getItem("x-auth-state")
        if (!authData) {
          window.location.href = "/"
          return
        }

        const { authToken, user: userData } = JSON.parse(authData)
        setUser(userData)

        const response = await fetch(`/api/v1/rental-advance/${params.id}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        })

        if (response.ok) {
          const data = await response.json()
          setRentalAdvance(data)
        }
      } catch (error) {
        console.error("Error loading data:", error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [params.id])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-600 to-green-700 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Carregando...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-600 to-green-700 relative overflow-hidden">
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
        <div className="absolute bottom-40 right-16 w-40 h-40 bg-white rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-white rounded-full blur-3xl"></div>
      </div>
      <div className="relative z-10 px-4 py-8 flex flex-col min-h-screen">
        <div className="flex justify-center mb-8">
          <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 border border-white/30 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={140} height={37} className="h-9 w-auto" />
          </div>
        </div>

        <div className="flex-1 flex flex-col justify-center text-center">
          <div className="mb-8">
            <div className="relative w-24 h-24 mx-auto mb-6">
              <div className="absolute inset-0 bg-white/20 rounded-full animate-ping"></div>
              <div className="relative w-full h-full bg-white rounded-full flex items-center justify-center shadow-2xl">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-white mb-4">Parabéns!</h1>
            <p className="text-white/90 text-lg mb-6">Sua antecipação foi solicitada com sucesso!</p>
            {rentalAdvance?.proposal && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 mb-6 max-w-sm mx-auto">
                <div className="text-2xl font-bold text-white mb-2">
                  {new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(rentalAdvance.proposal.netAmount)}
                </div>
                <p className="text-white/80 text-sm">Valor que você receberá</p>
              </div>
            )}
          </div>

          <div className="space-y-4 mb-8">
            <Card className="bg-white/95 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20 max-w-md mx-auto">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <Clock className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="text-left">
                    <h3 className="font-bold text-gray-800">Próximos Passos</h3>
                    <p className="text-sm text-gray-600">Acompanhe o andamento</p>
                  </div>
                </div>
                <div className="space-y-3 text-left">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700">Análise da documentação (até 2h)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <span className="text-sm text-gray-500">Aprovação final (até 24h)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <span className="text-sm text-gray-500">Transferência PIX (até 24h)</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            <Button
              onClick={() => window.location.href = "/dashboard"}
              className="w-full max-w-sm mx-auto bg-white text-green-600 hover:bg-gray-50 font-bold py-3 px-6 rounded-lg shadow-lg transition-all duration-200"
            >
              <Plus className="w-4 h-4 mr-2" />
              Voltar ao Dashboard
            </Button>
            <Button
              onClick={() =>
                window.open(
                  "https://wa.me/5541999999999?text=Olá! Acabei de solicitar uma antecipação e gostaria de acompanhar o status.",
                  "_blank",
                )
              }
              variant="ghost"
              className="text-white/80 hover:text-white hover:bg-white/10 text-sm border border-white/20 rounded-lg px-6 py-2 transition-all duration-200"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Acompanhar pelo WhatsApp
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-center gap-4 text-white/60 text-xs pt-8">
          <div className="flex items-center gap-1">
            <Shield className="w-3 h-3" />
            <span>Dados Protegidos</span>
          </div>
          <div className="flex items-center gap-1">
            <Award className="w-3 h-3" />
            <span>Certificado SSL</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-3 h-3" />
            <span>5 estrelas</span>
          </div>
        </div>
      </div>
    </div>
  )
}
