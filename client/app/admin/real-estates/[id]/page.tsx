"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { api } from "@/lib/api-handler"
import {
  ArrowLeft,
  Building,
  RefreshCw,
  AlertCircle,
  Eye,
  Calendar,
  DollarSign,
  User,
  FileText,
  Phone,
} from "lucide-react"

interface RealEstateDetails {
  id: string
  name: string
  cnpj: string
  createdAt: string
  totalRequests: number
}

interface RequestItem {
  id: string
  rentAmount: number
  monthsToAdvance: number
  proposalAmount?: number
  currentStatus: string
  createdAt: string
  updatedAt: string
  user: {
    name: string
    cpf: string
    phone?: string
  }
}

interface RealEstateDetailsResponse {
  realEstate: RealEstateDetails
  requests: RequestItem[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function RealEstateDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const realEstateId = params.id as string

  const [data, setData] = useState<RealEstateDetailsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [currentPage, setCurrentPage] = useState(1)

  const fetchRealEstateDetails = async (page: number = 1) => {
    setLoading(true)
    setError("")

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
      })

      const response = await api.adminGet<RealEstateDetailsResponse>(
        `admin/real-estates/${realEstateId}?${params}`
      )
      
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch real estate details")
      }

      setData(response.data!)
      setCurrentPage(page)
    } catch (error) {
      console.error("Error fetching real estate details:", error)
      setError("Erro ao carregar detalhes da imobiliária")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (realEstateId) {
      fetchRealEstateDetails()
    }
  }, [realEstateId])

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatCNPJ = (cnpj: string) => {
    return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5")
  }

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const formatPhone = (phone: string) => {
    if (!phone) return "Não informado"
    return phone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3")
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; variant: "default" | "secondary" | "destructive" | "outline" }> = {
      created: { label: "Criada", variant: "outline" },
      pdf_uploaded: { label: "PDF Enviado", variant: "outline" },
      pdf_extracted: { label: "Dados Extraídos", variant: "outline" },
      data_confirmed: { label: "Dados Confirmados", variant: "outline" },
      pending_proposal: { label: "Aguardando Proposta", variant: "secondary" },
      proposal_sent: { label: "Proposta Enviada", variant: "secondary" },
      docs_uploaded: { label: "Documentos Enviados", variant: "secondary" },
      awaiting_review: { label: "Em Análise", variant: "secondary" },
      approved: { label: "Aprovada", variant: "default" },
      rejected: { label: "Rejeitada", variant: "destructive" },
      cancelled: { label: "Cancelada", variant: "destructive" },
    }

    const config = statusMap[status] || { label: status, variant: "outline" as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const handleViewRequest = (requestId: string) => {
    router.push(`/admin/requests?requestId=${requestId}`)
  }

  if (loading && !data) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          <div className="h-8 bg-primary/20 rounded w-64 animate-pulse"></div>
        </div>
        
        <Card className="animate-pulse shadow-lg">
          <CardContent className="p-6">
            <div className="h-6 bg-primary/20 rounded mb-4"></div>
            <div className="h-4 bg-primary/20 rounded mb-2"></div>
            <div className="h-4 bg-primary/20 rounded w-1/2"></div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse shadow-lg">
              <CardContent className="p-6">
                <div className="h-4 bg-primary/20 rounded mb-2"></div>
                <div className="h-6 bg-primary/30 rounded mb-4"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-primary/20 rounded w-20"></div>
                  <div className="h-6 bg-primary/20 rounded w-24"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          <h2 className="text-3xl font-bold text-primary">Detalhes da Imobiliária</h2>
        </div>
        
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <p>{error}</p>
            </div>
            <Button 
              onClick={() => fetchRealEstateDetails(currentPage)} 
              className="mt-4 bg-primary hover:bg-primary/90"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          <div>
            <h2 className="text-3xl font-bold text-primary">{data.realEstate.name}</h2>
            <p className="text-muted-foreground">CNPJ: {formatCNPJ(data.realEstate.cnpj)}</p>
          </div>
        </div>
        <Button onClick={() => fetchRealEstateDetails(currentPage)} variant="outline" className="border-primary text-primary hover:bg-primary/10">
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      {/* Real Estate Info */}
      <Card className="shadow-lg border-l-4 border-l-primary">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-primary">
            <Building className="h-5 w-5" />
            Informações da Imobiliária
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <Calendar className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Cadastrada em</p>
                <p className="font-semibold">{formatDate(data.realEstate.createdAt)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <FileText className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total de Solicitações</p>
                <p className="font-semibold text-primary">{data.realEstate.totalRequests}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <Building className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">CNPJ</p>
                <p className="font-semibold">{formatCNPJ(data.realEstate.cnpj)}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requests List */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-primary">
            <FileText className="h-5 w-5" />
            Solicitações de Adiantamento ({data.pagination.total})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {data.requests.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhuma solicitação encontrada</h3>
              <p className="text-gray-600">Esta imobiliária ainda não possui solicitações de adiantamento.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {data.requests.map((request) => (
                <Card key={request.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="p-2 bg-primary/10 rounded-full">
                            <User className="h-4 w-4 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-primary">{request.user.name}</h4>
                            <p className="text-sm text-muted-foreground">CPF: {formatCPF(request.user.cpf)}</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            <div>
                              <p className="text-muted-foreground">Valor do Aluguel</p>
                              <p className="font-semibold">{formatCurrency(Number(request.rentAmount))}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-blue-600" />
                            <div>
                              <p className="text-muted-foreground">Meses</p>
                              <p className="font-semibold">{request.monthsToAdvance}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-purple-600" />
                            <div>
                              <p className="text-muted-foreground">Telefone</p>
                              <p className="font-semibold">{formatPhone(request.user.phone || "")}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-600" />
                            <div>
                              <p className="text-muted-foreground">Criada em</p>
                              <p className="font-semibold">{formatDate(request.createdAt)}</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 mt-3">
                          {getStatusBadge(request.currentStatus)}
                          {request.proposalAmount && (
                            <div className="text-sm">
                              <span className="text-muted-foreground">Proposta: </span>
                              <span className="font-semibold text-green-600">
                                {formatCurrency(Number(request.proposalAmount))}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => handleViewRequest(request.id)}
                        size="sm"
                        className="bg-primary hover:bg-primary/90"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Ver Detalhes
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {data.pagination.totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                onClick={() => fetchRealEstateDetails(currentPage - 1)}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
              >
                Anterior
              </Button>
              <span className="text-sm text-muted-foreground">
                Página {currentPage} de {data.pagination.totalPages}
              </span>
              <Button
                onClick={() => fetchRealEstateDetails(currentPage + 1)}
                disabled={currentPage === data.pagination.totalPages}
                variant="outline"
                size="sm"
              >
                Próxima
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
