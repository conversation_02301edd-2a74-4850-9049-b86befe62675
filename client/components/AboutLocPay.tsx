"use client"

import { useEffect, useState } from "react"
import { useInView } from "react-intersection-observer"
import SectionTitle from "./SectionTitle"

export default function AboutLocPay() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const [count1, setCount1] = useState(0)
  const [count2, setCount2] = useState(0)
  const [count3, setCount3] = useState(0)

  useEffect(() => {
    if (inView) {
      const duration = 2000 // Total duration for the animation
      const steps = 100 // Total steps for the animation

      const increment1 = 1000 / steps
      const increment2 = 5 / steps // Increment for "5M"
      const increment3 = 98 / steps
      const stepTime = duration / steps

      let currentStep = 0

      const timer = setInterval(() => {
        if (currentStep === steps) {
          clearInterval(timer)
          setCount1(1000)
          setCount2(5) // Ensure it reaches exactly "5"
          setCount3(98)
          return
        }

        setCount1((prev) => Math.min(Math.round(prev + increment1), 1000))
        setCount2((prev) => Math.min(prev + increment2, 5)) // Animate to "5"
        setCount3((prev) => Math.min(Math.round(prev + increment3), 98))

        currentStep++
      }, stepTime)

      return () => clearInterval(timer)
    }
  }, [inView])

  return (
    <section className="py-16 bg-gradient-to-br from-white to-gray-50">
      <div className="container mx-auto px-4">
        <SectionTitle
          badge="Sobre a LocPay"
          darkText="Criando Soluções"
          lightText={
            <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text">
              Feitas Para Você
            </span>
          }
          description="Com a nossa tecnologia inovadora, seu aluguel vai além do esperado. 🚀"
        />

        <div ref={ref} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 text-center">
          {/* Card 1 */}
          <div className="group p-6 rounded-lg bg-white shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
            <div className="space-y-2">
              <div className="text-4xl sm:text-5xl font-bold">
                <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text transition-transform duration-300 group-hover:scale-110">
                  +{count1}
                </span>
              </div>
              <div className="text-xl sm:text-2xl font-semibold">clientes atendidos</div>
              <p className="text-gray-600 text-sm sm:text-base">
                Realizamos o sonho de mais de 1.000 proprietários com a antecipação de seus aluguéis.
              </p>
            </div>
          </div>

          {/* Card 2 */}
          <div className="group p-6 rounded-lg bg-white shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
            <div className="space-y-2">
              <div className="text-4xl sm:text-5xl font-bold">
                <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text transition-transform duration-300 group-hover:scale-110">
                  +{count2.toFixed(1)}M
                </span>
              </div>
              <div className="text-xl sm:text-2xl font-semibold">antecipados</div>
              <p className="text-gray-600 text-sm sm:text-base">
                São mais de R$ 5 milhões antecipados para proprietários clientes das imobiliárias parceiras.
              </p>
            </div>
          </div>

          {/* Card 3 */}
          <div className="group p-6 rounded-lg bg-white shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
            <div className="space-y-2">
              <div className="text-4xl sm:text-5xl font-bold">
                <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text transition-transform duration-300 group-hover:scale-110">
                  {count3}%
                </span>
              </div>
              <div className="text-xl sm:text-2xl font-semibold">de satisfação</div>
              <p className="text-gray-600 text-sm sm:text-base">
                Nossos clientes recomendam a LocPay e retornam para novas antecipações.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
