"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Star, MessageSquare, User, Phone, CheckCircle, Sparkles, AlertCircle } from "lucide-react"
import Image from "next/image"

interface FormData {
  tipo: string
  nomeCompleto: string
  celular: string
  nota: string
  oQueGostou: string
  oQueMelhorar: string
  comentarioAdicional: string
}

interface FormErrors {
  tipo?: string
  nomeCompleto?: string
  celular?: string
  nota?: string
}

type FormState = "form" | "loading" | "success" | "error"

export function AvaliacaoForm() {
  const [formData, setFormData] = useState<FormData>({
    tipo: "",
    nomeCompleto: "",
    celular: "",
    nota: "",
    oQueGostou: "",
    oQueMelhorar: "",
    comentarioAdicional: "",
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [formState, setFormState] = useState<FormState>("form")

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, "")
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3")
    } else {
      return numbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3")
    }
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value)
    setFormData((prev) => ({ ...prev, celular: formatted }))
    if (errors.celular) {
      setErrors((prev) => ({ ...prev, celular: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.tipo) {
      newErrors.tipo = "Por favor, selecione uma opção"
    }

    if (!formData.nomeCompleto.trim()) {
      newErrors.nomeCompleto = "Nome completo é obrigatório"
    }

    const phoneNumbers = formData.celular.replace(/\D/g, "")
    if (!phoneNumbers || phoneNumbers.length < 10) {
      newErrors.celular = "Celular deve ter pelo menos 10 dígitos"
    }

    if (!formData.nota) {
      newErrors.nota = "Por favor, selecione uma nota"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setFormState("loading")

    try {
      const response = await fetch("https://locpay.app.n8n.cloud/webhook/avaliacao-clientes-parceiros", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tipo: formData.tipo,
          nomeCompleto: formData.nomeCompleto,
          celular: formData.celular,
          nota: formData.nota,
          oQueGostou: formData.oQueGostou,
          oQueMelhorar: formData.oQueMelhorar,
          comentarioAdicional: formData.comentarioAdicional,
          dataEnvio: new Date().toISOString(),
        }),
      })

      if (response.ok) {
        setFormState("success")
      } else {
        setFormState("error")
      }
    } catch (error) {
      console.error("Erro ao enviar avaliação:", error)
      setFormState("error")
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star key={index} className={`w-5 h-5 ${index < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
    ))
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    if (errors[field as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }))
    }
  }

  if (formState === "loading") {
    return (
      <Card className="bg-gradient-to-br from-blue-50 via-white to-[#F0F7FE] border-2 border-blue-200 shadow-2xl rounded-3xl overflow-hidden">
        <CardContent className="p-8 text-center">
          <div className="relative mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-[#00A7E1] to-[#6BBAED] rounded-full flex items-center justify-center mx-auto shadow-2xl">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-[#074377] mb-4">Enviando avaliação...</h3>
          <p className="text-gray-600">Aguarde um momento enquanto processamos sua avaliação.</p>
        </CardContent>
      </Card>
    )
  }

  if (formState === "success") {
    return (
      <Card className="bg-gradient-to-br from-green-50 via-white to-[#F0F7FE] border-2 border-green-200 shadow-2xl rounded-3xl overflow-hidden">
        <CardContent className="p-8 text-center">
          <div className="relative mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-2xl">
              <CheckCircle className="w-10 h-10 text-white" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
          </div>
          <h3 className="text-3xl font-black text-[#074377] mb-4">Obrigado pelo seu feedback!</h3>
          <p className="text-lg text-gray-600 leading-relaxed font-medium">
            Sua opinião nos ajuda a melhorar cada vez mais. 🙏
          </p>
        </CardContent>
      </Card>
    )
  }

  if (formState === "error") {
    return (
      <Card className="bg-gradient-to-br from-red-50 via-white to-[#F0F7FE] border-2 border-red-200 shadow-2xl rounded-3xl overflow-hidden">
        <CardContent className="p-8 text-center">
          <div className="relative mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto shadow-2xl">
              <AlertCircle className="w-10 h-10 text-white" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-red-600 mb-4">Ocorreu um erro ao enviar sua avaliação</h3>
          <p className="text-gray-600 mb-6">Por favor, tente novamente.</p>
          <Button
            onClick={() => setFormState("form")}
            className="bg-gradient-to-r from-[#074377] to-[#00A7E1] hover:from-[#0090c0] hover:to-[#0080b0] text-white font-bold px-8 py-3 rounded-2xl transition-all duration-300 hover:scale-105 shadow-lg"
          >
            Tentar Novamente
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden">
      {/* Header do formulário */}
      <CardHeader className="relative bg-gradient-to-br from-[#003366] via-[#074377] to-[#004B87] px-6 pt-6 pb-4 overflow-hidden">
        {/* Efeitos visuais de movimento */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-pulse"></div>
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full animate-bounce"></div>
          <div className="absolute -bottom-2 -left-2 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
        </div>

        <div className="relative z-10 text-center space-y-4">
          {/* Logo LocPay com fundo azul escuro */}
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="absolute inset-0 bg-[#003366] rounded-xl blur-sm"></div>
              <div className="relative bg-[#003366] backdrop-blur-sm rounded-xl px-4 py-2 border border-[#074377]">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                  alt="LocPay"
                  width={120}
                  height={40}
                  className="h-8 w-auto"
                />
              </div>
            </div>
          </div>

          <CardTitle className="text-2xl md:text-3xl font-black text-white leading-tight tracking-tight">
            💬 Avaliação da Experiência LocPay
          </CardTitle>

          {/* Badge processo digital */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-sm font-bold border border-white/30">
            <MessageSquare className="w-4 h-4" />
            <span>Seu feedback é muito importante</span>
            <Sparkles className="w-4 h-4 animate-bounce" />
          </div>
        </div>
      </CardHeader>

      {/* Formulário */}
      <CardContent className="px-6 pb-6 bg-gradient-to-br from-white/90 to-[#F0F7FE]/50">
        <form onSubmit={handleSubmit} className="space-y-6 pt-6">
          {/* Grid de campos - 2 colunas em telas maiores */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tipo de usuário */}
            <div className="space-y-2 md:col-span-2">
              <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>Você é?</span>
                <span className="text-red-500">*</span>
              </label>
              <Select value={formData.tipo} onValueChange={(value) => handleInputChange("tipo", value)}>
                <SelectTrigger
                  className={`w-full h-12 px-4 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] ${
                    errors.tipo
                      ? "border-red-500 focus:border-red-500 shadow-red-100"
                      : "border-gray-200 focus:border-[#00A7E1] hover:border-gray-300 focus:shadow-[#00A7E1]/20"
                  } focus:ring-0 focus:outline-none focus:shadow-lg`}
                >
                  <SelectValue placeholder="Selecione uma opção" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cliente-proprietario">Cliente Proprietário</SelectItem>
                  <SelectItem value="imobiliaria-parceira">Imobiliária Parceira</SelectItem>
                </SelectContent>
              </Select>
              {errors.tipo && (
                <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {errors.tipo}
                </p>
              )}
            </div>

            {/* Nome Completo */}
            <div className="space-y-2">
              <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>Nome Completo</span>
                <span className="text-red-500">*</span>
              </label>
              <Input
                type="text"
                value={formData.nomeCompleto}
                onChange={(e) => handleInputChange("nomeCompleto", e.target.value)}
                className={`w-full h-12 px-4 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                  errors.nomeCompleto
                    ? "border-red-500 focus:border-red-500 shadow-red-100"
                    : "border-gray-200 focus:border-[#00A7E1] hover:border-gray-300 focus:shadow-[#00A7E1]/20"
                } focus:ring-0 focus:outline-none focus:shadow-lg`}
                placeholder="Digite seu nome completo"
              />
              {errors.nomeCompleto && (
                <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {errors.nomeCompleto}
                </p>
              )}
            </div>

            {/* Celular */}
            <div className="space-y-2">
              <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                <Phone className="w-4 h-4" />
                <span>Celular</span>
                <span className="text-red-500">*</span>
              </label>
              <Input
                type="tel"
                value={formData.celular}
                onChange={handlePhoneChange}
                className={`w-full h-12 px-4 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                  errors.celular
                    ? "border-red-500 focus:border-red-500 shadow-red-100"
                    : "border-gray-200 focus:border-[#00A7E1] hover:border-gray-300 focus:shadow-[#00A7E1]/20"
                } focus:ring-0 focus:outline-none focus:shadow-lg`}
                placeholder="(11) 99999-9999"
                maxLength={15}
              />
              {errors.celular && (
                <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {errors.celular}
                </p>
              )}
            </div>
          </div>

          {/* Nota - Largura completa */}
          <div className="space-y-2">
            <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
              <Star className="w-4 h-4" />
              <span>Nota geral da experiência com a LocPay</span>
              <span className="text-red-500">*</span>
            </label>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map((rating) => (
                <label
                  key={rating}
                  className={`flex items-center space-x-3 p-4 rounded-2xl border-2 cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                    formData.nota === rating.toString()
                      ? "border-[#00A7E1] bg-gradient-to-r from-[#00A7E1]/10 to-[#80C2F4]/10 shadow-lg"
                      : "border-gray-200 hover:border-gray-300 bg-white hover:shadow-md"
                  }`}
                >
                  <input
                    type="radio"
                    name="nota"
                    value={rating}
                    checked={formData.nota === rating.toString()}
                    onChange={(e) => handleInputChange("nota", e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex items-center space-x-3">
                    <div className="flex">{renderStars(rating)}</div>
                    <span className="text-sm font-medium text-gray-700">
                      {rating === 1 && "Muito insatisfeito"}
                      {rating === 2 && "Insatisfeito"}
                      {rating === 3 && "Neutro"}
                      {rating === 4 && "Satisfeito"}
                      {rating === 5 && "Muito satisfeito"}
                    </span>
                  </div>
                </label>
              ))}
            </div>
            {errors.nota && (
              <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {errors.nota}
              </p>
            )}
          </div>

          {/* Campos de texto - Grid de 1 coluna */}
          <div className="space-y-6">
            {/* O que mais gostou */}
            <div className="space-y-2">
              <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                <span>O que mais gostou no processo?</span>
                <span className="text-xs text-gray-500 font-normal">(opcional)</span>
              </label>
              <Textarea
                value={formData.oQueGostou}
                onChange={(e) => handleInputChange("oQueGostou", e.target.value)}
                className="min-h-[100px] px-4 py-3 text-sm bg-white border-2 border-gray-200 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] focus:border-[#00A7E1] hover:border-gray-300 focus:shadow-[#00A7E1]/20 focus:ring-0 focus:outline-none focus:shadow-lg resize-none"
                placeholder="Conte-nos o que mais gostou na sua experiência..."
              />
            </div>

            {/* O que podemos melhorar */}
            <div className="space-y-2">
              <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                <span>O que podemos melhorar?</span>
                <span className="text-xs text-gray-500 font-normal">(opcional)</span>
              </label>
              <Textarea
                value={formData.oQueMelhorar}
                onChange={(e) => handleInputChange("oQueMelhorar", e.target.value)}
                className="min-h-[100px] px-4 py-3 text-sm bg-white border-2 border-gray-200 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] focus:border-[#00A7E1] hover:border-gray-300 focus:shadow-[#00A7E1]/20 focus:ring-0 focus:outline-none focus:shadow-lg resize-none"
                placeholder="Suas sugestões de melhoria são muito valiosas..."
              />
            </div>

            {/* Comentário adicional */}
            <div className="space-y-2">
              <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                <span>Comentário adicional</span>
                <span className="text-xs text-gray-500 font-normal">(opcional)</span>
              </label>
              <Textarea
                value={formData.comentarioAdicional}
                onChange={(e) => handleInputChange("comentarioAdicional", e.target.value)}
                className="min-h-[100px] px-4 py-3 text-sm bg-white border-2 border-gray-200 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] focus:border-[#00A7E1] hover:border-gray-300 focus:shadow-[#00A7E1]/20 focus:ring-0 focus:outline-none focus:shadow-lg resize-none"
                placeholder="Algum comentário adicional que gostaria de compartilhar..."
              />
            </div>
          </div>

          {/* Botão de envio */}
          <Button
            type="submit"
            className="w-full h-14 bg-gradient-to-r from-[#074377] via-[#00A7E1] to-[#6BBAED] hover:from-[#0090c0] hover:via-[#0080b0] hover:to-[#5aa8db] text-white font-black text-base rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl mt-8 relative overflow-hidden group"
          >
            {/* Efeito de brilho no botão */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            <div className="flex items-center justify-center gap-3">
              <span>ENVIAR AVALIAÇÃO</span>
            </div>
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
