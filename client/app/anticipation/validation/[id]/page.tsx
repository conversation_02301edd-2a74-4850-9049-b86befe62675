"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Smile,
  FileText,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  ChevronLeft,
  X,
  CheckCircle2,
  XCircle,
  RefreshCw,
} from "lucide-react"
import Image from "next/image"

interface ExtractedData {
  nomeCompleto: string
  celular: string
  email: string
  Inquilino: string
  Proprietário: string
  Imóvel: string
  "Valor do Aluguel": string
  "Vigência do Contrato": string
  Imobiliária: string
}

interface RentalAdvance {
  id: string
  status: string
  extractedData: ExtractedData
  rentAmount: number
  anticipationMonths: number
  realEstate: {
    name: string
  }
}

export default function ValidationPage({ params }: { params: { id: string } }) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [rentalAdvance, setRentalAdvance] = useState<RentalAdvance | null>(null)
  const [confirming, setConfirming] = useState(false)
  const [isExtracting, setIsExtracting] = useState(true)

  useEffect(() => {
    const loadRentalAdvance = async () => {
      try {
        const authData = localStorage.getItem("x-auth-state")
        if (!authData) {
          window.location.href = "/"
          return
        }

        const { authToken } = JSON.parse(authData)

        const response = await fetch(`/api/v1/rental-advance/${params.id}/extracted-data`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        })

        if (response.ok) {
          const data = await response.json()
          setRentalAdvance(data)
          setIsExtracting(false)
        } else if (response.status === 404) {
          // Dados ainda não foram extraídos, continuar polling
          setIsExtracting(true)
        } else {
          setError("Erro ao carregar dados extraídos")
          setIsExtracting(false)
        }
      } catch (error) {
        console.error("Error loading rental advance:", error)
        setError("Erro de conexão. Tente novamente.")
        setIsExtracting(false)
      } finally {
        setLoading(false)
      }
    }

    loadRentalAdvance()

    // Polling a cada 10 segundos se ainda estiver extraindo
    const pollInterval = setInterval(() => {
      if (isExtracting && !rentalAdvance) {
        loadRentalAdvance()
      }
    }, 10000)

    return () => clearInterval(pollInterval)
  }, [params.id, isExtracting, rentalAdvance])

  const confirmData = async () => {
    setConfirming(true)
    setError("")
    setSuccess("")

    try {
      const authData = localStorage.getItem("x-auth-state")
      if (!authData) {
        window.location.href = "/"
        return
      }

      const { authToken } = JSON.parse(authData)

      const response = await fetch("/api/v1/rental-advance/confirm-data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          operationId: params.id,
          extractedData: rentalAdvance?.extractedData || {},
        }),
      })

      if (response.ok) {
        setSuccess("Dados confirmados! Gerando proposta...")
        setTimeout(() => {
          window.location.href = `/anticipation/proposal/${params.id}`
        }, 1500)
      } else {
        const result = await response.json()
        setError(result.message || "Erro ao confirmar dados")
      }
    } catch (error) {
      console.error("Error confirming data:", error)
      setError("Erro de conexão. Tente novamente.")
    } finally {
      setConfirming(false)
    }
  }

  const handleIncorrectData = () => {
    window.location.href = "/anticipation"
  }

  if (loading || isExtracting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-6"></div>
          <h2 className="text-xl font-bold text-white mb-4">
            {loading ? "Carregando..." : "Extraindo dados do contrato"}
          </h2>
          <p className="text-white/80 mb-4">
            {loading
              ? "Aguarde enquanto carregamos os dados..."
              : "Estamos analisando seu contrato de aluguel. Isso pode levar alguns minutos."
            }
          </p>
          {isExtracting && (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 max-w-sm mx-auto">
              <p className="text-white/90 text-sm">
                ⏱️ Tempo estimado: 2-5 minutos
              </p>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (!rentalAdvance) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center">
          <p className="text-white mb-4">Erro ao carregar dados extraídos</p>
          <Button
            onClick={() => window.location.href = "/dashboard"}
            className="bg-white text-blue-600 hover:bg-gray-100"
          >
            Voltar ao Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/anticipation"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Voltar"
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => window.location.href = "/dashboard"}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Fechar"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>

      <div className="px-4">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-white mb-2">Confirme os Dados Extraídos</h1>
          <p className="text-white/90">Verifique se as informações do contrato estão corretas</p>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm mb-4">
            {error}
          </div>
        )}
        {success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm mb-4">
            {success}
          </div>
        )}

        <Card className="bg-white shadow-xl rounded-3xl border-0 mb-6">
          <CardHeader className="bg-gradient-to-r from-[#0B4375] to-[#0B4375] text-white p-4 rounded-t-3xl">
            <CardTitle className="text-lg font-bold text-center">Dados Extraídos do Contrato</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-100">
              <div className="p-4 flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <Smile className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 font-medium mb-1">Inquilino</p>
                  <p className="font-bold text-[#0B4375] text-lg">
                    {rentalAdvance.extractedData?.Inquilino || "Não extraído"}
                  </p>
                </div>
              </div>
              <div className="p-4 flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <FileText className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 font-medium mb-1">Proprietário</p>
                  <p className="font-bold text-[#0B4375] text-lg">
                    {rentalAdvance.extractedData?.Proprietário || "Não extraído"}
                  </p>
                </div>
              </div>
              <div className="p-4 flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 font-medium mb-1">Imóvel</p>
                  <p className="font-bold text-[#0B4375] text-lg">
                    {rentalAdvance.extractedData?.Imóvel || "Não extraído"}
                  </p>
                </div>
              </div>
              <div className="p-4 flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 font-medium mb-1">Valor do Aluguel</p>
                  <p className="font-bold text-[#0B4375] text-lg">
                    {rentalAdvance.extractedData?.["Valor do Aluguel"] || 
                     new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(rentalAdvance.rentAmount)}
                  </p>
                </div>
              </div>
              <div className="p-4 flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 font-medium mb-1">Vigência do Contrato</p>
                  <p className="font-bold text-[#0B4375] text-lg">
                    {rentalAdvance.extractedData?.["Vigência do Contrato"] || "Não extraído"}
                  </p>
                </div>
              </div>
              <div className="p-4 flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <Building className="h-5 w-5 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 font-medium mb-1">Imobiliária</p>
                  <p className="font-bold text-[#0B4375] text-lg">
                    {rentalAdvance.extractedData?.Imobiliária || rentalAdvance.realEstate?.name || "Não extraído"}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          <Button 
            onClick={confirmData} 
            className="w-full h-12 text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
            disabled={confirming}
          >
            <CheckCircle2 className="w-5 h-5 mr-2" />
            {confirming ? "CONFIRMANDO..." : "DADOS CORRETOS, VER PROPOSTA"}
          </Button>
          <Button 
            onClick={handleIncorrectData} 
            className="w-full h-10 text-sm font-medium bg-red-50 border border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300 transition-all duration-200 rounded-lg"
          >
            <XCircle className="w-4 h-4 mr-2" />
            Dados incorretos, corrigir
          </Button>
        </div>
      </div>
    </div>
  )
}
