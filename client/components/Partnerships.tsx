"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Building, Users, TrendingUp } from "lucide-react"
import Image from "next/image"

export default function Partnerships() {
  const partnerBenefits = [
    {
      icon: Building,
      title: "Aumente sua Receita",
      description:
        "Ofereça um serviço adicional aos seus clientes e aumente sua receita com nossas comissões atrativas.",
    },
    {
      icon: Users,
      title: "Fidelize seus Clientes",
      description: "Proporcione mais valor aos seus clientes, aumentando a satisfação e fidelização.",
    },
    {
      icon: TrendingUp,
      title: "Cresça com a LocPay",
      description: "Aproveite nossa tecnologia e expertise para impulsionar o crescimento do seu negócio.",
    },
  ]

  const handleWhatsAppClick = () => {
    window.open(
      "https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0",
      "_blank",
    )
  }

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Cabeçalho */}
        <div className="text-center mb-12">
          <span className="inline-block px-4 py-1.5 bg-[#00A7E1]/10 text-[#00A7E1] rounded-full text-sm font-medium mb-4">
            Parcerias LocPay
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-[#1B3B5A] mb-4">
            Seja uma Imobiliária{" "}
            <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text">
              Parceira LocPay
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Com a LocPay, você aumenta a fidelização dos seus clientes e se diferencia da concorrência! 🤝
          </p>
        </div>

        {/* Benefícios */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {partnerBenefits.map((benefit, index) => (
            <Card key={index} className="hover:shadow-lg transition-all">
              <CardHeader>
                <div className="h-12 w-12 rounded-lg bg-[#004B87]/10 flex items-center justify-center mb-4">
                  <benefit.icon className="h-6 w-6 text-[#004B87]" />
                </div>
                <CardTitle className="text-[#004B87]">{benefit.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{benefit.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Botão atualizado */}
        <div className="flex justify-center">
          <Button
            size="lg"
            className="flex items-center gap-4 bg-[#074377] hover:bg-[#0090c0] text-white font-bold px-6 py-5 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl"
            onClick={handleWhatsAppClick}
          >
            Torne-se um Parceiro LocPay
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
              alt="LocPay"
              width={90}
              height={30}
              className="h-5 w-auto sm:h-7"
            />
          </Button>
        </div>
      </div>
    </section>
  )
}
