import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Logger } from '@nestjs/common';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * Armazena um valor no cache
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      await this.cacheManager.set(key, value, ttl);
      this.logger.debug(`Cache set: ${key}`);
    } catch (error) {
      this.logger.error(`Erro ao definir cache para ${key}:`, error);
    }
  }

  /**
   * Recupera um valor do cache
   */
  async get<T>(key: string): Promise<T | undefined> {
    try {
      const value = await this.cacheManager.get<T>(key);
      this.logger.debug(`Cache get: ${key} - ${value ? 'HIT' : 'MISS'}`);
      return value;
    } catch (error) {
      this.logger.error(`Erro ao buscar cache para ${key}:`, error);
      return undefined;
    }
  }

  /**
   * Remove um valor do cache
   */
  async del(key: string): Promise<void> {
    try {
      await this.cacheManager.del(key);
      this.logger.debug(`Cache deleted: ${key}`);
    } catch (error) {
      this.logger.error(`Erro ao deletar cache para ${key}:`, error);
    }
  }

  /**
   * Limpa todo o cache (não disponível na interface padrão)
   */
  reset(): void {
    try {
      // Para Redis, podemos usar o store diretamente se necessário
      this.logger.warn(
        'Cache reset não implementado - use del() para chaves específicas',
      );
    } catch (error) {
      this.logger.error('Erro ao limpar cache:', error);
    }
  }

  /**
   * Verifica se uma chave existe no cache
   */
  async has(key: string): Promise<boolean> {
    try {
      const value = await this.cacheManager.get(key);
      return value !== undefined;
    } catch (error) {
      this.logger.error(`Erro ao verificar cache para ${key}:`, error);
      return false;
    }
  }

  /**
   * Gera uma chave de cache para operações de usuário
   */
  generateUserKey(userId: string, operation: string): string {
    return `user:${userId}:${operation}`;
  }

  /**
   * Gera uma chave de cache para operações gerais
   */
  generateKey(prefix: string, ...parts: string[]): string {
    return `${prefix}:${parts.join(':')}`;
  }
}
