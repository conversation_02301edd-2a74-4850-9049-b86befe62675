"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Settings, Download, Trash2 } from "lucide-react"

interface ChatSettings {
  welcomeMessage: string
  maxTokens: number
  temperature: number
  isEnabled: boolean
}

export default function ChatSettingsPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [settings, setSettings] = useState<ChatSettings>({
    welcomeMessage: "Olá! Eu sou o LocBot, assistente virtual da LocPay...",
    maxTokens: 1024,
    temperature: 0.7,
    isEnabled: true,
  })

  const exportConversations = async () => {
    try {
      const response = await fetch("/api/chat/export", {
        method: "GET",
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = `chat-conversations-${new Date().toISOString().split("T")[0]}.json`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error("Erro ao exportar conversas:", error)
    }
  }

  const clearAllConversations = async () => {
    if (confirm("Tem certeza que deseja limpar todas as conversas? Esta ação não pode ser desfeita.")) {
      try {
        await fetch("/api/chat/clear", {
          method: "DELETE",
        })
        alert("Conversas limpas com sucesso!")
      } catch (error) {
        console.error("Erro ao limpar conversas:", error)
      }
    }
  }

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed top-4 right-4 z-40 bg-gray-800 hover:bg-gray-700"
        size="icon"
      >
        <Settings className="w-4 h-4" />
      </Button>
    )
  }

  return (
    <div className="fixed top-4 right-4 z-40 w-80">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Configurações do Chat
            <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)}>
              ×
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Mensagem de Boas-vindas</label>
            <Textarea
              value={settings.welcomeMessage}
              onChange={(e) => setSettings((prev) => ({ ...prev, welcomeMessage: e.target.value }))}
              className="mt-1"
              rows={3}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Max Tokens</label>
            <Input
              type="number"
              value={settings.maxTokens}
              onChange={(e) => setSettings((prev) => ({ ...prev, maxTokens: Number.parseInt(e.target.value) }))}
              className="mt-1"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Temperature</label>
            <Input
              type="number"
              step="0.1"
              min="0"
              max="1"
              value={settings.temperature}
              onChange={(e) => setSettings((prev) => ({ ...prev, temperature: Number.parseFloat(e.target.value) }))}
              className="mt-1"
            />
          </div>

          <div className="flex gap-2">
            <Button onClick={exportConversations} variant="outline" size="sm" className="flex-1">
              <Download className="w-4 h-4 mr-1" />
              Exportar
            </Button>
            <Button onClick={clearAllConversations} variant="destructive" size="sm" className="flex-1">
              <Trash2 className="w-4 h-4 mr-1" />
              Limpar
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
