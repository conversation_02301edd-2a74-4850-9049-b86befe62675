"use client"

import React from "react"
import { useState, use<PERSON>ffect, useR<PERSON>, use<PERSON><PERSON>back } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>hart, Pie, Cell, Tooltip, ResponsiveContainer } from "recharts"
import {
  Loader2,
  Check,
  AlertTriangle,
  PartyPopper,
  FileSignature,
  Settings2,
  X,
  ChevronLeft,
  CreditCard,
  Smartphone,
  Mail,
  Smile,
  Building,
  CalendarDays,
  FileText,
  Zap,
  Clock,
  Lock,
  <PERSON><PERSON><PERSON>,
  Calcula<PERSON>,
  TrendingUp,
} from "lucide-react"
import Image from "next/image"

// --- Tipos e Estado Inicial ---
interface FormData {
  nomeCompleto: string
  cpf: string
  celular: string
  email: string
  prazoDesejado: string
  imobiliaria: string
  contratoFileName: string
}

interface AppState {
  step: number
  subStep: number
  loading: boolean
  loadingMsg: string
  formData: FormData
  codigoSMS: string
  proposalMonths: number
  error: string
}

const INITIAL_STATE: AppState = {
  step: 1,
  subStep: 1,
  loading: false,
  loadingMsg: "",
  formData: {
    nomeCompleto: "João Silva",
    cpf: "123.456.789-00",
    celular: "(41) 99999-0000",
    email: "<EMAIL>",
    prazoDesejado: "12",
    imobiliaria: "Imobiliária Alfa",
    contratoFileName: "",
  },
  codigoSMS: "",
  proposalMonths: 6,
  error: "",
}

// --- Dados Mockados para Simulação ---
const MOCK_EXTRACTED_DATA = {
  Inquilino: "João da Silva Santos",
  Proprietário: "Maria Oliveira Costa",
  Imóvel: "Rua das Palmeiras, 456, São Paulo/SP",
  "Valor do Aluguel": "R$ 2.800,00",
  "Vigência do Contrato": "01/01/2024 - 31/12/2026",
}

const MOCK_PROPOSAL_DATA = {
  valorBase: 2800,
  taxaMensalPercent: 2.17,
}

const IMOBILIARIAS_EXEMPLO = [
  "A Predial",
  "Mega Imóveis",
  "Fiducial Imobiliária",
  "Inov9 Imóveis",
  "Estafor",
  "Américo Timbó Imobiliária",
  "Triiio",
  "Rafael Rabelo",
  "Praia de Iracema Imóveis",
  "FCBS",
  "Ciro Paiva",
  "7 Cantos",
  "Alessandro Belchior Imóveis",
  "Outros",
]

const PRAZOS_DESEJADOS = [
  { value: "6", label: "6 meses" },
  { value: "12", label: "12 meses" },
  { value: "18", label: "18 meses" },
  { value: "24", label: "24 meses" },
]

// --- Componente Principal ---
export default function LocPayMockup() {
  const [state, setState] = useState<AppState>(INITIAL_STATE)
  const firstInputRef = useRef<HTMLInputElement>(null)
  const [showQuickNav, setShowQuickNav] = useState(false)

  const maskCPF = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d{1,2})/, "$1-$2")
        .replace(/(-\d{2})\d+?$/, "$1"),
    [],
  )

  const maskPhone = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{2})(\d)/, "($1) $2")
        .replace(/(\d{5})(\d)/, "$1-$2")
        .replace(/(-\d{4})\d+?$/, "$1"),
    [],
  )

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const parseCurrency = (value: string) => {
    const onlyDigits = value.replace(/\D/g, "")
    if (!onlyDigits) return 0
    return Number(onlyDigits) / 100
  }

  const formatInputCurrency = (value: string) => {
    const numberValue = parseCurrency(value)
    if (numberValue === 0 && (value === "" || value === "R$ 0,00" || value === "R$0,00")) return ""
    return formatCurrency(numberValue)
  }

  const startLoading = useCallback((min: number, max: number, msg: string, onComplete: () => void) => {
    setState((prev) => ({ ...prev, loading: true, loadingMsg: msg, error: "" }))
    const duration = Math.random() * (max - min) + min
    setTimeout(() => {
      onComplete()
      setState((prev) => ({ ...prev, loading: false, loadingMsg: "" }))
      setTimeout(() => firstInputRef.current?.focus(), 100)
    }, duration)
  }, [])

  useEffect(() => {
    try {
      const savedState = localStorage.getItem("locpay_mockup_state_v9")
      if (savedState) {
        const parsed = JSON.parse(savedState)
        if (parsed.proposalMonths && typeof parsed.proposalMonths !== "number") {
          parsed.proposalMonths = Number(parsed.proposalMonths) || INITIAL_STATE.proposalMonths
        }
        setState((prev) => ({ ...prev, ...parsed, loading: false }))
      }
    } catch (e) {
      console.error("Failed to load state", e)
    }
  }, [])

  useEffect(() => {
    localStorage.setItem("locpay_mockup_state_v9", JSON.stringify(state))
  }, [state])

  const handlePreviousStep = () => {
    setState((prev) => {
      if (prev.subStep > 1) return { ...prev, subStep: prev.subStep - 1, error: "" }
      if (prev.step > 1)
        return { ...prev, step: prev.step - 1, subStep: prev.step === 2 ? 1 : prev.step === 1 ? 2 : 1, error: "" }
      return prev
    })
  }

  const QuickNavigation = () => (
    <Card className="my-4 bg-gradient-to-br from-[#F0F7FE] to-white border-2 border-[#00A7E1]/20 shadow-xl rounded-3xl">
      <CardHeader className="pb-2 pt-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-sm text-[#074377] flex items-center font-bold">
            <Settings2 className="h-4 w-4 mr-2 text-[#074377]" />
            Navegação Rápida
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowQuickNav(false)}
            className="text-[#074377] hover:bg-[#074377]/10 h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="flex flex-wrap gap-2 justify-center">
          {[
            { step: 1, subStep: 1, label: "Cadastro" },
            { step: 1, subStep: 2, label: "SMS" },
            { step: 2, subStep: 1, label: "Validação" },
            { step: 3, subStep: 1, label: "Proposta" },
            { step: 4, subStep: 1, label: "Assinatura" },
            { step: 5, subStep: 1, label: "Sucesso" },
          ].map((navItem) => (
            <Button
              key={`${navItem.step}-${navItem.subStep}`}
              onClick={() =>
                setState((prev) => ({
                  ...prev,
                  step: navItem.step,
                  subStep: navItem.subStep,
                  loading: false,
                  error: "",
                }))
              }
              variant={state.step === navItem.step && state.subStep === navItem.subStep ? "default" : "outline"}
              size="sm"
              className={`${
                state.step === navItem.step && state.subStep === navItem.subStep
                  ? "bg-[#00A7E1] hover:bg-[#00A7E1]/90 text-white text-xs font-bold"
                  : "border-[#00A7E1] text-[#00A7E1] hover:bg-[#00A7E1]/10 text-xs font-medium"
              } rounded-xl`}
            >
              {navItem.label}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )

  const StepIndicator = () => {
    const stepsConfig = [
      { number: 1, label: "Cadastro" },
      { number: 2, label: "Validação" },
      { number: 3, label: "Proposta" },
      { number: 4, label: "Assinatura" },
    ]
    const currentMainStep = state.step

    return (
      <div className="flex items-start justify-between w-full px-2 mb-8">
        {stepsConfig.map((stepItem, index) => (
          <React.Fragment key={stepItem.number}>
            <div className="flex flex-col items-center text-center w-1/4">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 border-2 shadow-lg ${
                  currentMainStep > stepItem.number
                    ? "bg-gradient-to-r from-[#074377] to-[#00A7E1] text-white border-[#00A7E1]"
                    : currentMainStep === stepItem.number
                      ? "bg-gradient-to-r from-[#074377] to-[#00A7E1] text-white border-[#00A7E1]"
                      : "bg-white text-gray-400 border-gray-300"
                }`}
              >
                {currentMainStep > stepItem.number ? <Check className="h-4 w-4" /> : stepItem.number}
              </div>
              <span
                className={`text-xs mt-2 font-bold ${
                  currentMainStep === stepItem.number ? "text-[#074377]" : "text-gray-400"
                }`}
              >
                {stepItem.label}
              </span>
            </div>
            {index < stepsConfig.length - 1 && (
              <div
                className={`flex-1 h-0.5 mt-4 rounded mx-2 transition-all duration-300 ${
                  currentMainStep > stepItem.number ? "bg-gradient-to-r from-[#074377] to-[#00A7E1]" : "bg-gray-300"
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    )
  }

  const LoadingScreen = () => (
    <div className="fixed inset-0 bg-gradient-to-br from-white/95 to-[#F0F7FE]/95 flex items-center justify-center z-50 backdrop-blur-sm animate-fade-in">
      <div className="text-center p-8 bg-white rounded-3xl shadow-2xl border-2 border-[#00A7E1]/20">
        <div className="relative mb-6">
          <div className="w-16 h-16 bg-gradient-to-r from-[#074377] to-[#00A7E1] rounded-full flex items-center justify-center mx-auto shadow-2xl">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
            <Sparkles className="w-3 h-3 text-white" />
          </div>
        </div>
        <p className="text-[#074377] font-bold text-lg mb-2" aria-live="polite">
          {state.loadingMsg}
        </p>
        <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
          <Clock className="w-4 h-4" />
          <span>Aguarde um momento...</span>
        </div>
      </div>
    </div>
  )

  const commonInputClass =
    "h-12 bg-white border-2 border-gray-200 focus:border-[#00A7E1] focus:ring-2 focus:ring-[#00A7E1]/20 rounded-2xl text-base placeholder:text-gray-400 transition-all duration-300 focus:scale-[1.02] shadow-sm hover:border-gray-300"
  const inputWithIconClass = "pl-12 pr-4"
  const mainButtonClass =
    "w-full h-14 text-base font-black bg-gradient-to-r from-[#074377] via-[#00A7E1] to-[#6BBAED] hover:from-[#0090c0] hover:via-[#0080b0] hover:to-[#5aa8db] text-white transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl rounded-2xl relative overflow-hidden group"
  const greenButtonClass =
    "w-full h-14 text-base font-black bg-gradient-to-r from-[#28a745] to-[#20c997] hover:from-[#218838] hover:to-[#1ea085] text-white transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl rounded-2xl"
  const outlineButtonClass =
    "w-full h-12 text-base font-bold border-2 border-[#00A7E1] text-[#00A7E1] hover:bg-[#00A7E1]/5 rounded-2xl transition-all duration-300"

  const PageHeader = ({ showBackArrow = true }: { showBackArrow?: boolean }) => (
    <div className="flex items-center justify-between mb-6 px-1 pt-4">
      {showBackArrow && (state.step > 1 || state.subStep > 1) ? (
        <Button
          variant="ghost"
          size="icon"
          onClick={handlePreviousStep}
          className="text-[#074377] hover:bg-[#074377]/10 w-10 h-10 rounded-2xl"
        >
          <ChevronLeft className="h-6 w-6" />
        </Button>
      ) : (
        <div className="w-10 h-10" />
      )}

      <div className="flex justify-center">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-[#074377] to-[#00A7E1] rounded-2xl blur-sm opacity-20"></div>
          <div className="relative bg-gradient-to-r from-[#074377] to-[#00A7E1] backdrop-blur-sm rounded-2xl px-4 py-2 border border-[#074377]/20">
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
              alt="LocPay"
              width={120}
              height={40}
              className="h-8 w-auto"
            />
          </div>
        </div>
      </div>

      <div className="w-10 h-10" />
    </div>
  )

  const InputField = ({
    icon: Icon,
    id,
    placeholder,
    value,
    onChange,
    maxLength,
    type = "text",
    inputRef,
  }: {
    icon?: React.ComponentType<any>
    id: string
    placeholder: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    maxLength?: number
    type?: string
    inputRef?: React.RefObject<HTMLInputElement>
  }) => (
    <div className="relative">
      {Icon && <Icon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#074377]" />}
      <Input
        ref={inputRef}
        id={id}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        maxLength={maxLength}
        className={`${commonInputClass} ${Icon ? inputWithIconClass : "px-4"}`}
      />
    </div>
  )

  const Page1_Form = () => {
    const handleInputChange = (field: keyof FormData, value: string | boolean) =>
      setState((prev) => ({ ...prev, formData: { ...prev.formData, [field]: value }, error: "" }))

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (file && file.type === "application/pdf") handleInputChange("contratoFileName", file.name)
      else if (file) setState((prev) => ({ ...prev, error: "Por favor, envie um arquivo PDF." }))
      else handleInputChange("contratoFileName", "")
    }

    const handleSubmit = () => {
      startLoading(1000, 1500, "Enviando código de verificação...", () => setState((prev) => ({ ...prev, subStep: 2 })))
    }

    return (
      <div className="animate-fade-in bg-gradient-to-r from-[#EBF3FF] to-[#D4E7FF] min-h-screen">
        <PageHeader />
        <div className="px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-black text-[#074377] leading-tight mb-2">🚀 Vamos começar!</h1>
            <p className="text-lg text-gray-600 font-medium">Informe seus dados pessoais</p>
          </div>

          <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden mb-6">
            <CardContent className="space-y-6 p-6">
              <InputField
                icon={Smile}
                id="nomeCompleto"
                placeholder="Nome completo"
                value={state.formData.nomeCompleto}
                onChange={(e: any) => handleInputChange("nomeCompleto", e.target.value)}
                inputRef={firstInputRef}
              />

              <InputField
                icon={CreditCard}
                id="cpf"
                placeholder="CPF"
                value={state.formData.cpf}
                onChange={(e: any) => handleInputChange("cpf", maskCPF(e.target.value))}
                maxLength={14}
              />

              <InputField
                icon={Smartphone}
                id="celular"
                placeholder="Celular"
                value={state.formData.celular}
                onChange={(e: any) => handleInputChange("celular", maskPhone(e.target.value))}
                maxLength={15}
              />

              <InputField
                icon={Mail}
                id="email"
                placeholder="E-mail"
                type="email"
                value={state.formData.email}
                onChange={(e: any) => handleInputChange("email", e.target.value)}
              />

              <div className="relative">
                <CalendarDays className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#074377]" />
                <Select
                  value={state.formData.prazoDesejado}
                  onValueChange={(value) => handleInputChange("prazoDesejado", value)}
                >
                  <SelectTrigger
                    className={`${commonInputClass} ${inputWithIconClass} ${state.formData.prazoDesejado ? "text-[#074377]" : "text-gray-400"}`}
                  >
                    <SelectValue placeholder="Prazo desejado para antecipação" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRAZOS_DESEJADOS.map((p) => (
                      <SelectItem key={p.value} value={p.value}>
                        {p.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="relative">
                <Building className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[#074377]" />
                <Select
                  value={state.formData.imobiliaria}
                  onValueChange={(value) => handleInputChange("imobiliaria", value)}
                >
                  <SelectTrigger
                    className={`${commonInputClass} ${inputWithIconClass} ${state.formData.imobiliaria ? "text-[#074377]" : "text-gray-400"}`}
                  >
                    <SelectValue placeholder="Imobiliária administradora" />
                  </SelectTrigger>
                  <SelectContent>
                    {IMOBILIARIAS_EXEMPLO.map((imob) => (
                      <SelectItem key={imob} value={imob}>
                        {imob}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <label
                htmlFor="contrato"
                className={`flex items-center justify-start w-full ${commonInputClass} px-4 rounded-2xl cursor-pointer transition-all duration-300 ${
                  state.formData.contratoFileName
                    ? "border-[#00A7E1] text-[#00A7E1] bg-[#F0F7FE]"
                    : "text-gray-400 border-gray-200 hover:border-gray-300"
                }`}
              >
                <FileText
                  className={`w-5 h-5 mr-3 ${state.formData.contratoFileName ? "text-[#00A7E1]" : "text-gray-400"}`}
                />
                <span className="text-sm font-medium">
                  {state.formData.contratoFileName || "Contrato de locação (PDF)"}
                </span>
              </label>
              <Input id="contrato" type="file" accept=".pdf" onChange={handleFileChange} className="hidden" />
            </CardContent>
          </Card>

          {state.error && (
            <Alert variant="destructive" className="mb-6 bg-red-50 border-2 border-red-200 text-red-700 rounded-2xl">
              <AlertTriangle className="h-5 w-5 text-red-700" />
              <AlertDescription className="font-medium">{state.error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4 mb-6">
            <Button onClick={handleSubmit} size="lg" className={mainButtonClass}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              <span className="flex items-center justify-center gap-2">
                CONTINUAR
                <Zap className="w-5 h-5" />
              </span>
            </Button>

            <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
              <Lock className="w-4 h-4 text-[#074377]" />
              <span className="font-medium">Seus dados estão protegidos com criptografia</span>
            </div>
          </div>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const Page1_SMS = () => {
    const handleSubmit = () => {
      startLoading(1500, 2500, "Validando seu código...", () => setState((prev) => ({ ...prev, step: 2 })))
    }

    const slotClassName =
      "h-16 w-12 text-xl font-bold rounded-2xl border-2 border-gray-300 focus:border-[#00A7E1] focus:ring-2 focus:ring-[#00A7E1]/20 bg-white shadow-lg transition-all duration-300"

    return (
      <div className="animate-fade-in bg-gradient-to-r from-[#EBF3FF] to-[#D4E7FF] min-h-screen">
        <PageHeader />
        <div className="px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-black text-[#074377] mb-2">📱 Validação</h1>
            <p className="text-lg text-gray-600 font-medium">Confirme o código enviado</p>
          </div>

          <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden mb-6">
            <CardContent className="flex flex-col items-center gap-6 p-8">
              <p className="text-gray-600 text-center text-base leading-relaxed font-medium">
                Ótimo! Agora, só precisamos validar os dados do seu contrato. <br />
                <span className="font-bold text-[#074377]">Informe o código enviado para {state.formData.celular}</span>
              </p>

              <div className="p-8 bg-gradient-to-br from-[#F0F7FE] to-white rounded-3xl shadow-xl border-2 border-[#00A7E1]/20 w-full max-w-sm mx-auto">
                <p className="text-base text-[#074377] text-center mb-6 font-bold">Código de validação</p>
                <InputOTP
                  ref={firstInputRef}
                  maxLength={6}
                  value={state.codigoSMS}
                  onChange={(value) => setState((prev) => ({ ...prev, codigoSMS: value, error: "" }))}
                >
                  <InputOTPGroup className="gap-3 justify-center">
                    {[0, 1, 2, 3, 4, 5].map((i) => (
                      <InputOTPSlot key={i} index={i} className={slotClassName} />
                    ))}
                  </InputOTPGroup>
                </InputOTP>
                <Button variant="link" className="text-sm text-[#00A7E1] hover:text-[#00A7E1]/80 mt-6 w-full font-bold">
                  Reenviar código
                </Button>
              </div>
            </CardContent>
          </Card>

          {state.error && (
            <Alert variant="destructive" className="mb-6 bg-red-50 border-2 border-red-200 text-red-700 rounded-2xl">
              <AlertTriangle className="h-5 w-5 text-red-700" />
              <AlertDescription className="font-medium">{state.error}</AlertDescription>
            </Alert>
          )}

          <Button onClick={handleSubmit} size="lg" className={mainButtonClass}>
            <span className="flex items-center justify-center gap-2">
              CONFIRMAR CÓDIGO
              <Check className="w-5 h-5" />
            </span>
          </Button>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const Page2 = () => {
    const confirmData = () =>
      startLoading(1500, 3000, "Consultando seu crédito e gerando a melhor proposta...", () =>
        setState((prev) => ({ ...prev, step: 3 })),
      )

    return (
      <div className="animate-fade-in bg-gradient-to-r from-[#EBF3FF] to-[#D4E7FF] min-h-screen">
        <PageHeader />
        <div className="px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-black text-[#074377] mb-2">✨ Confira os dados extraídos</h1>
            <p className="text-lg text-gray-600 font-medium">Nossa IA leu seu contrato. Tudo certo?</p>
          </div>

          <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden mb-6">
            <CardContent className="space-y-4 p-6">
              {Object.entries(MOCK_EXTRACTED_DATA).map(([key, value]) => (
                <div
                  key={key}
                  className="p-4 border-2 border-gray-100 rounded-2xl bg-gradient-to-r from-white to-[#F0F7FE]/30 shadow-sm hover:shadow-md transition-all duration-300"
                >
                  <p className="text-sm text-gray-500 font-medium mb-1">{key}</p>
                  <p className="font-bold text-[#074377] text-base">{value}</p>
                </div>
              ))}
            </CardContent>
          </Card>

          <Button onClick={confirmData} className={mainButtonClass}>
            <span className="flex items-center justify-center gap-2">
              DADOS CORRETOS, VER PROPOSTA
              <TrendingUp className="w-5 h-5" />
            </span>
          </Button>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const Page3 = () => {
    const displayNetValue = 13750
    const displayTaxValue = 250
    const displayTotalValue = displayNetValue + displayTaxValue
    const displayTaxRate = (displayTaxValue / displayTotalValue) * 100

    const chartData = [
      { name: "Valor Líquido", value: displayNetValue, fill: "#00A7E1" },
      { name: "Taxa LocPay", value: displayTaxValue, fill: "#28a745" },
    ]

    const handleMonthsChange = (value: number) => {
      setState((prev) => ({ ...prev, proposalMonths: value }))
    }

    return (
      <div className="animate-fade-in bg-gradient-to-r from-[#EBF3FF] to-[#D4E7FF] min-h-screen">
        <PageHeader />
        <div className="px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-black text-[#074377] mb-2">🎉 Parabéns! Proposta aprovada</h1>
            <p className="text-lg text-gray-600 font-medium">Sua antecipação foi aprovada!</p>
          </div>

          <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden mb-6">
            <CardContent className="space-y-8 p-6">
              <div className="text-center">
                <div className="relative mb-4">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#074377] to-[#00A7E1] rounded-3xl blur-lg opacity-20"></div>
                  <div className="relative bg-gradient-to-r from-[#074377] to-[#00A7E1] rounded-3xl p-6 text-white">
                    <p className="text-4xl font-black mb-2">{formatCurrency(displayNetValue)}</p>
                    <p className="text-lg font-medium opacity-90">Valor a receber na sua conta</p>
                  </div>
                </div>
              </div>

              <div className="h-56 w-full max-w-sm mx-auto">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      innerRadius={50}
                      paddingAngle={3}
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} stroke={entry.fill} strokeWidth={2} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: number, name: string) => [formatCurrency(value), name]} />
                  </PieChart>
                </ResponsiveContainer>
                <div className="text-center -mt-2 text-sm">
                  <div className="flex justify-center gap-4">
                    <span className="inline-flex items-center">
                      <span className="h-3 w-3 rounded-full bg-[#00A7E1] mr-2"></span>
                      <span className="font-medium text-[#074377]">Valor Líquido</span>
                    </span>
                    <span className="inline-flex items-center">
                      <span className="h-3 w-3 rounded-full bg-[#28a745] mr-2"></span>
                      <span className="font-medium text-[#074377]">Taxa ({displayTaxRate.toFixed(2)}%)</span>
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-[#F0F7FE] to-white p-6 rounded-2xl border-2 border-[#00A7E1]/20">
                <label htmlFor="anticipationMonths" className="block text-base font-bold text-[#074377] mb-3">
                  Meses de antecipação: {state.proposalMonths} meses
                </label>
                <div className="flex items-center gap-4">
                  <Slider
                    id="anticipationMonths"
                    value={[state.proposalMonths]}
                    onValueChange={([val]) => handleMonthsChange(val)}
                    min={1}
                    max={12}
                    step={1}
                    className="flex-1 [&>span:first-child]:h-3 [&>span:first-child]:bg-gradient-to-r [&>span:first-child]:from-[#074377] [&>span:first-child]:to-[#00A7E1] [&>span:first-child_span]:bg-white [&>span:first-child_span]:h-6 [&>span:first-child_span]:w-6 [&>span:first-child_span]:border-4 [&>span:first-child_span]:border-[#00A7E1] [&>span:first-child_span]:shadow-xl"
                  />
                  <Input
                    type="number"
                    value={state.proposalMonths}
                    onChange={(e) => {
                      const value = Number.parseInt(e.target.value)
                      if (!isNaN(value) && value >= 1 && value <= 12) {
                        handleMonthsChange(value)
                      }
                    }}
                    min={1}
                    max={12}
                    className="w-20 h-12 text-base font-bold text-[#074377] border-2 border-gray-200 focus:border-[#00A7E1] focus:ring-2 focus:ring-[#00A7E1]/20 rounded-2xl shadow-sm"
                  />
                </div>
                <div className="flex justify-between text-sm text-gray-500 mt-2 font-medium">
                  <span>1 mês</span>
                  <span className="font-bold text-[#074377]">{state.proposalMonths} meses selecionados</span>
                  <span>12 meses</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-4">
            <Button
              onClick={() =>
                startLoading(1000, 2000, "Gerando seu contrato...", () => setState((prev) => ({ ...prev, step: 4 })))
              }
              className={greenButtonClass}
            >
              <span className="flex items-center justify-center gap-2">
                SIM, ACEITO! ASSINAR CONTRATO
                <FileSignature className="w-5 h-5" />
              </span>
            </Button>

            <Button onClick={() => setState(INITIAL_STATE)} variant="outline" className={outlineButtonClass}>
              Agora não
            </Button>
          </div>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const Page4 = () => {
    const [pixKey, setPixKey] = useState("123.456.***-**")
    const [termsAccepted, setTermsAccepted] = useState(false)

    const finalizar = () => {
      startLoading(1500, 2500, "Processando sua assinatura digital...", () =>
        setState((prev) => ({ ...prev, step: 5 })),
      )
    }

    const displayNetValue = 13750
    const displayTotalAlugueis = 14000

    return (
      <div className="animate-fade-in bg-gradient-to-r from-[#EBF3FF] to-[#D4E7FF] min-h-screen">
        <PageHeader />
        <div className="px-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-black text-[#074377] leading-tight mb-2">🏁 Estamos quase lá!</h1>
            <p className="text-lg text-gray-600 font-medium">Confirme os detalhes finais</p>
          </div>

          <div className="space-y-6 mb-6">
            <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-[#074377] to-[#00A7E1] text-white p-4">
                <CardTitle className="text-lg font-bold flex items-center gap-2">
                  <Calculator className="w-5 h-5" />
                  Detalhes da Operação
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-base p-6">
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-[#F0F7FE] to-white rounded-2xl">
                  <span className="text-gray-600 font-medium">Valor dos Aluguéis:</span>
                  <span className="font-bold text-[#074377]">{formatCurrency(displayTotalAlugueis)}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-[#F0F7FE] to-white rounded-2xl">
                  <span className="text-gray-600 font-medium">Valor Líquido a Receber:</span>
                  <span className="font-black text-[#00A7E1] text-lg">{formatCurrency(displayNetValue)}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-[#F0F7FE] to-white rounded-2xl">
                  <span className="text-gray-600 font-medium">Chave PIX:</span>
                  <span className="font-bold text-[#074377]">{pixKey}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377]/10 rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-[#074377] to-[#00A7E1] text-white p-4">
                <CardTitle className="text-lg font-bold flex items-center gap-2">
                  <FileSignature className="w-5 h-5" />
                  Assinatura Eletrônica
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="w-full h-32 bg-gradient-to-br from-[#F0F7FE] to-white border-dashed border-2 border-[#00A7E1] rounded-2xl flex items-center justify-center">
                  <div className="text-center">
                    <FileSignature className="h-12 w-12 text-[#00A7E1] mx-auto mb-2" />
                    <p className="text-sm text-gray-500 font-medium">Assinatura Digital Automática</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex items-start space-x-3 p-4 bg-gradient-to-r from-[#F0F7FE] to-white border-2 border-[#00A7E1]/20 rounded-2xl">
              <Checkbox
                id="terms"
                checked={termsAccepted}
                onCheckedChange={(checked) => {
                  setTermsAccepted(Boolean(checked))
                  setState((prev) => ({ ...prev, error: "" }))
                }}
                className="mt-1 border-2 border-gray-300 data-[state=checked]:bg-[#00A7E1] data-[state=checked]:border-[#00A7E1] rounded-lg w-5 h-5"
              />
              <label htmlFor="terms" className="text-sm text-gray-700 leading-relaxed cursor-pointer">
                <span className="font-bold text-[#074377]">Declaro que li e aceito os</span>{" "}
                <Button
                  variant="link"
                  className="p-0 h-auto text-[#00A7E1] hover:text-[#00A7E1]/80 text-sm font-bold underline"
                >
                  Termos de Cessão de Crédito
                </Button>{" "}
                <span className="font-bold text-[#074377]">e confirmo minha assinatura digital.</span>
              </label>
            </div>
          </div>

          {state.error && (
            <Alert variant="destructive" className="mb-6 bg-red-50 border-2 border-red-200 text-red-700 rounded-2xl">
              <AlertTriangle className="h-5 w-5 text-red-700" />
              <AlertDescription className="font-medium">{state.error}</AlertDescription>
            </Alert>
          )}

          <Button onClick={finalizar} size="lg" className={greenButtonClass}>
            <span className="flex items-center justify-center gap-2">
              FINALIZAR CONTRATO
              <Check className="w-5 h-5" />
            </span>
          </Button>
        </div>
        <StepIndicator />
      </div>
    )
  }

  const SuccessScreen = () => (
    <div className="animate-fade-in text-center py-8 px-6 bg-gradient-to-r from-[#EBF3FF] to-[#D4E7FF] min-h-screen">
      <PageHeader showBackArrow={false} />

      <div className="flex flex-col items-center justify-center flex-1 space-y-8 mt-12">
        <div className="relative">
          <div className="w-24 h-24 bg-gradient-to-r from-[#28a745] to-[#20c997] rounded-full flex items-center justify-center mx-auto shadow-2xl">
            <PartyPopper className="text-white h-12 w-12" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
        </div>

        <div className="space-y-4">
          <h1 className="text-4xl font-black text-[#074377] leading-tight">Solicitação Enviada com Sucesso! 🎉</h1>
          <p className="text-lg text-gray-600 leading-relaxed max-w-md mx-auto font-medium">
            Parabéns, <span className="font-bold text-[#074377]">{state.formData.nomeCompleto.split(" ")[0]}</span>! Sua
            solicitação foi concluída. O valor será transferido para sua conta PIX em até{" "}
            <span className="font-black text-[#00A7E1]">12 horas úteis</span>.
          </p>
        </div>

        <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#28a745]/20 rounded-3xl overflow-hidden max-w-sm mx-auto">
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center gap-3 text-sm">
              <div className="w-2 h-2 bg-[#28a745] rounded-full"></div>
              <span className="text-gray-600 font-medium">Transferência em até 12h</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <div className="w-2 h-2 bg-[#28a745] rounded-full"></div>
              <span className="text-gray-600 font-medium">Processo 100% digital</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <div className="w-2 h-2 bg-[#28a745] rounded-full"></div>
              <span className="text-gray-600 font-medium">Acompanhe por WhatsApp</span>
            </div>
          </CardContent>
        </Card>

        <Button
          onClick={() => {
            localStorage.removeItem("locpay_mockup_state_v9")
            setState(INITIAL_STATE)
          }}
          size="lg"
          className={`${mainButtonClass} max-w-xs mx-auto`}
        >
          <span className="flex items-center justify-center gap-2">
            FAZER NOVA SIMULAÇÃO
            <Zap className="w-5 h-5" />
          </span>
        </Button>
      </div>
      <StepIndicator />
    </div>
  )

  const renderStep = () => {
    switch (state.step) {
      case 1:
        return state.subStep === 1 ? <Page1_Form /> : <Page1_SMS />
      case 2:
        return <Page2 />
      case 3:
        return <Page3 />
      case 4:
        return <Page4 />
      case 5:
        return <SuccessScreen />
      default:
        return <Page1_Form />
    }
  }

  return (
    <div className="w-full max-w-md mx-auto bg-white sm:rounded-3xl sm:shadow-2xl my-0 sm:my-4 font-sans overflow-hidden relative">
      {state.loading && <LoadingScreen />}
      {showQuickNav && <QuickNavigation />}
      <div className="min-h-screen sm:min-h-[600px]">{renderStep()}</div>

      {/* Quick Nav Toggle - Only for development */}
      <Button
        onClick={() => setShowQuickNav(!showQuickNav)}
        className="fixed bottom-4 right-4 w-12 h-12 rounded-full bg-[#074377] hover:bg-[#074377]/90 text-white shadow-2xl z-50 sm:hidden"
        size="icon"
      >
        <Settings2 className="w-5 h-5" />
      </Button>
    </div>
  )
}
