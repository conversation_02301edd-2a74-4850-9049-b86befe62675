"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"

export default function FinalCTA() {
  const handleWhatsAppClick = () => {
    window.open(
      "https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0",
      "_blank",
    )
  }

  return (
    <section className="py-16 bg-gradient-to-br from-[#1B3B5A] to-[#00A7E1] text-white">
      <div className="container mx-auto px-4 text-center">
        <span className="inline-block px-4 py-1.5 bg-white/10 text-white rounded-full text-sm font-medium mb-4">
          Comece Agora
        </span>
        <h2 className="text-4xl md:text-5xl font-bold mb-6">
          Seu aluguel quando você{" "}
          <span className="bg-gradient-to-r from-white to-[#00A7E1] text-transparent bg-clip-text">
            mais precisa 🏠
          </span>
        </h2>
        <p className="text-xl md:text-2xl mb-4 text-blue-100 max-w-3xl mx-auto">
          Comece a antecipar seu aluguel hoje mesmo e realize seus sonhos! ✨
        </p>
        <p className="text-lg md:text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
          Junte-se a milhares de proprietários que já escolheram a LocPay para ter mais liberdade financeira.
        </p>
        <div className="flex flex-col md:flex-row justify-center items-center gap-4">
          <Button
            size="lg"
            variant="secondary"
            className="group bg-white text-[#1B3B5A] hover:bg-[#00A7E1] hover:text-white transition-all duration-300 transform hover:scale-105"
            onClick={handleWhatsAppClick}
          >
            Fazer Simulação Agora
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="group bg-transparent border-white text-white hover:bg-white hover:text-[#1B3B5A] transition-all duration-300 transform hover:scale-105"
            onClick={handleWhatsAppClick}
          >
            Solicitar Parceria com Imobiliária
          </Button>
        </div>
        <p className="mt-6 text-sm text-blue-100">
          Sua imobiliária ainda não é parceira? Solicite Integração com a LocPay Imediatamente
        </p>
      </div>
    </section>
  )
}
