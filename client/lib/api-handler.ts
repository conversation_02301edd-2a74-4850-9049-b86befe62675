/**
 * Generic API handler for all backend requests
 * Eliminates code duplication and provides consistent error handling
 */

import { config } from './config'

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  headers?: Record<string, string>
  requireAuth?: boolean
  isAdmin?: boolean
}

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  status: number
}

class ApiHandler {
  private baseUrl: string
  private defaultHeaders: Record<string, string>

  constructor() {
    this.baseUrl = config.api.baseUrl
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null
    
    try {
      const savedState = localStorage.getItem(config.auth.tokenKey)
      if (!savedState) return null
      
      const parsed = JSON.parse(savedState)
      return parsed.authToken || null
    } catch {
      return null
    }
  }

  private buildHeaders(options: ApiOptions): Record<string, string> {
    const headers = { ...this.defaultHeaders, ...options.headers }
    
    if (options.requireAuth || options.isAdmin) {
      const token = this.getAuthToken()
      if (token) {
        headers.Authorization = `Bearer ${token}`
      }
    }
    
    return headers
  }

  private buildUrl(endpoint: string): string {
    // Remove leading slash if present
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint
    
    // Add /api prefix if not present
    const apiEndpoint = cleanEndpoint.startsWith('api/') ? cleanEndpoint : `api/${cleanEndpoint}`
    
    return `${this.baseUrl}/${apiEndpoint}`
  }

  async request<T = any>(endpoint: string, options: ApiOptions = {}): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      body,
      requireAuth = false,
      isAdmin = false,
    } = options

    try {
      // Check auth requirements
      if ((requireAuth || isAdmin) && !this.getAuthToken()) {
        return {
          success: false,
          error: 'Token de autenticação não encontrado',
          status: 401,
        }
      }

      const url = this.buildUrl(endpoint)
      const headers = this.buildHeaders(options)

      const fetchOptions: RequestInit = {
        method,
        headers,
      }

      if (body && method !== 'GET') {
        fetchOptions.body = JSON.stringify(body)
      }

      const response = await fetch(url, fetchOptions)
      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
        }
      }

      return {
        success: true,
        data,
        status: response.status,
      }
    } catch (error) {
      console.error('API Request failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro de conexão',
        status: 0,
      }
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, options: Omit<ApiOptions, 'method'> = {}) {
    return this.request<T>(endpoint, { ...options, method: 'GET' })
  }

  async post<T = any>(endpoint: string, body?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}) {
    return this.request<T>(endpoint, { ...options, method: 'POST', body })
  }

  async put<T = any>(endpoint: string, body?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}) {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body })
  }

  async delete<T = any>(endpoint: string, options: Omit<ApiOptions, 'method'> = {}) {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' })
  }

  // Admin-specific methods
  async adminGet<T = any>(endpoint: string, options: Omit<ApiOptions, 'method' | 'isAdmin'> = {}) {
    return this.request<T>(endpoint, { ...options, method: 'GET', isAdmin: true })
  }

  async adminPost<T = any>(endpoint: string, body?: any, options: Omit<ApiOptions, 'method' | 'body' | 'isAdmin'> = {}) {
    return this.request<T>(endpoint, { ...options, method: 'POST', body, isAdmin: true })
  }
}

// Export singleton instance
export const api = new ApiHandler()

// Export types for use in components
export type { ApiResponse, ApiOptions }
