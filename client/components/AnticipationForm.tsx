"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { X, Upload, CheckCircle, AlertCircle, FileText, Lock, Clock, Zap } from "lucide-react"
import Image from "next/image"

interface FormData {
  nome: string
  celular: string
  cpf: string
  aluguelLiquido: string
  mesesAntecipados: string
  imobiliaria: string
  contrato: File | null
  consentimento: boolean
}

interface AnticipationFormProps {
  isOpen: boolean
  onClose: () => void
}

export default function AnticipationForm({ isOpen, onClose }: AnticipationFormProps) {
  const [formData, setFormData] = useState<FormData>({
    nome: "",
    celular: "",
    cpf: "",
    aluguelLiquido: "",
    mesesAntecipados: "",
    imobiliaria: "",
    contrato: null,
    consentimento: false,
  })
  const [errors, setErrors] = useState<Partial<FormData>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [isError, setIsError] = useState(false)

  // Bloquear popup quando form estiver aberto
  useEffect(() => {
    if (typeof window !== "undefined") {
      if (isOpen) {
        // Adicionar flag para bloquear popup
        window.locpayFormOpen = true
      } else {
        // Remover flag quando fechar
        window.locpayFormOpen = false
      }

      return () => {
        window.locpayFormOpen = false
      }
    }
  }, [isOpen])

  const imobiliarias = [
    "A Predial",
    "Mega Imóveis",
    "Fiducial Imobiliária",
    "Inov9 Imóveis",
    "Estafor",
    "Américo Timbó Imobiliária",
    "Triiio",
    "Rafael Rabelo",
    "Praia de Iracema Imóveis",
    "FCBS",
    "Ciro Paiva",
    "7 Cantos",
    "Alessandro Belchior Imóveis",
    "Amauri Gomes Imóveis",
    "Ary Brasil Administração de Imóveis",
    "César Rêgo Imóveis",
    "DCS Imobiliária",
    "DMV",
    "Espindola Imobiliária",
    "Ferreira Lopes Imobiliária",
    "FZ Imóveis",
    "Imobiliária Magno Muniz",
    "Business",
    "Fz Imóveis",
    "Outros",
  ]

  // Máscara para celular brasileiro
  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, "")
    if (numbers.length <= 11) {
      return numbers.replace(/(\d{2})(\d)/, "($1) $2").replace(/(\d{4,5})(\d{4})$/, "$1-$2")
    }
    return value
  }

  // Máscara para CPF
  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, "")
    if (numbers.length <= 11) {
      return numbers
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d{1,2})$/, "$1-$2")
    }
    return value
  }

  // Máscara para valor monetário
  const formatCurrency = (value: string) => {
    const numbers = value.replace(/\D/g, "")
    const amount = Number.parseInt(numbers) / 100
    return amount.toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
    })
  }

  // Validação de CPF
  const isValidCPF = (cpf: string) => {
    const numbers = cpf.replace(/\D/g, "")
    if (numbers.length !== 11) return false

    // Verifica se todos os dígitos são iguais
    if (/^(\d)\1{10}$/.test(numbers)) return false

    // Validação dos dígitos verificadores
    let sum = 0
    for (let i = 0; i < 9; i++) {
      sum += Number.parseInt(numbers[i]) * (10 - i)
    }
    let digit1 = 11 - (sum % 11)
    if (digit1 > 9) digit1 = 0

    sum = 0
    for (let i = 0; i < 10; i++) {
      sum += Number.parseInt(numbers[i]) * (11 - i)
    }
    let digit2 = 11 - (sum % 11)
    if (digit2 > 9) digit2 = 0

    return digit1 === Number.parseInt(numbers[9]) && digit2 === Number.parseInt(numbers[10])
  }

  // Validação do formulário
  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {}

    if (!formData.nome.trim()) {
      newErrors.nome = "Nome é obrigatório"
    }

    if (!formData.celular.trim()) {
      newErrors.celular = "Celular é obrigatório"
    } else if (formData.celular.replace(/\D/g, "").length < 10) {
      newErrors.celular = "Celular deve ter pelo menos 10 dígitos"
    }

    if (!formData.cpf.trim()) {
      newErrors.cpf = "CPF é obrigatório"
    } else if (!isValidCPF(formData.cpf)) {
      newErrors.cpf = "CPF inválido"
    }

    if (!formData.aluguelLiquido.trim()) {
      newErrors.aluguelLiquido = "Valor do aluguel é obrigatório"
    }

    if (!formData.mesesAntecipados.trim()) {
      newErrors.mesesAntecipados = "Número de meses é obrigatório"
    } else {
      const meses = Number.parseInt(formData.mesesAntecipados)
      if (meses < 1) {
        newErrors.mesesAntecipados = "Mínimo 1 mês"
      } else if (meses > 12) {
        newErrors.mesesAntecipados = "A LocPay só antecipa até 12 meses"
      }
    }

    if (!formData.imobiliaria) {
      newErrors.imobiliaria = "Selecione uma imobiliária"
    }

    if (!formData.contrato) {
      newErrors.contrato = "Upload do contrato é obrigatório"
    }

    if (!formData.consentimento) {
      newErrors.consentimento = "É necessário aceitar os termos para continuar"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Atualizar campos do formulário
  const handleInputChange = (field: keyof FormData, value: string | File | null | boolean) => {
    if (field === "celular" && typeof value === "string") {
      value = formatPhone(value)
    } else if (field === "cpf" && typeof value === "string") {
      value = formatCPF(value)
    } else if (field === "aluguelLiquido" && typeof value === "string") {
      value = formatCurrency(value)
    }

    setFormData((prev) => ({ ...prev, [field]: value }))

    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }))
    }
  }

  // Upload de arquivo - APENAS PDF
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.type !== "application/pdf") {
        setErrors((prev) => ({ ...prev, contrato: "Apenas arquivos PDF são aceitos" }))
        return
      }
      if (file.size > 20 * 1024 * 1024) {
        setErrors((prev) => ({ ...prev, contrato: "Arquivo deve ter no máximo 20MB" }))
        return
      }
      handleInputChange("contrato", file)
    }
  }

  // Envio do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    setIsError(false)

    try {
      // Formatar celular: remover formatação e adicionar 55
      const celularLimpo = formData.celular.replace(/\D/g, "")
      const celularFormatado = `55${celularLimpo}`

      // Preparar dados para envio
      const formDataToSend = new FormData()
      formDataToSend.append("nome", formData.nome)
      formDataToSend.append("celular", celularFormatado)
      formDataToSend.append("cpf", formData.cpf.replace(/\D/g, ""))
      formDataToSend.append("aluguelLiquido", formData.aluguelLiquido.replace(/\D/g, ""))
      formDataToSend.append("mesesAntecipados", formData.mesesAntecipados)
      formDataToSend.append("imobiliaria", formData.imobiliaria)
      if (formData.contrato) {
        formDataToSend.append("contrato", formData.contrato)
      }
      formDataToSend.append("consentimento", formData.consentimento.toString())

      console.log("Enviando dados para webhook:", {
        nome: formData.nome,
        celular: celularFormatado,
        cpf: formData.cpf.replace(/\D/g, ""),
        aluguelLiquido: formData.aluguelLiquido.replace(/\D/g, ""),
        mesesAntecipados: formData.mesesAntecipados,
        imobiliaria: formData.imobiliaria,
        contrato: formData.contrato?.name,
        consentimento: formData.consentimento,
      })

      // Webhook oficial do N8N - PRODUÇÃO
      const response = await fetch("https://locpay.app.n8n.cloud/webhook/7db33c72-6ce5-4702-ad82-bac418afc03f", {
        method: "POST",
        body: formDataToSend,
      })

      console.log("Response status:", response.status)

      if (response.ok || response.status === 200 || response.status === 201) {
        setIsSuccess(true)
        // Reset form
        setFormData({
          nome: "",
          celular: "",
          cpf: "",
          aluguelLiquido: "",
          mesesAntecipados: "",
          imobiliaria: "",
          contrato: null,
          consentimento: false,
        })
      } else {
        throw new Error(`Erro HTTP: ${response.status}`)
      }
    } catch (error) {
      console.error("Erro ao enviar formulário:", error)
      setIsError(true)
    } finally {
      setIsLoading(false)
    }
  }

  // Fechar modal - GARANTINDO QUE FUNCIONA
  const handleClose = () => {
    console.log("Fechando formulário...") // Debug
    setIsSuccess(false)
    setIsError(false)
    setErrors({})
    setFormData({
      nome: "",
      celular: "",
      cpf: "",
      aluguelLiquido: "",
      mesesAntecipados: "",
      imobiliaria: "",
      contrato: null,
      consentimento: false,
    })
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4 animate-in fade-in-0 duration-300 overflow-y-auto">
      {/* Background decorativo igual ao popup */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-radial from-[#E8F5FF]/20 via-[#F5FAFE]/10 to-transparent opacity-60" />
        <div className="absolute -top-10 right-0 w-96 h-96 bg-[#80C2F4]/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-20 left-0 w-72 h-72 bg-[#074377]/5 rounded-full blur-2xl" />
      </div>

      <Card className="relative w-full max-w-md mx-auto bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377] rounded-3xl overflow-hidden animate-in zoom-in-95 duration-300 my-8 max-h-[90vh] overflow-y-auto">
        {!isSuccess ? (
          <>
            {/* Header com gradiente mais escuro */}
            <div className="relative bg-gradient-to-br from-[#003366] via-[#074377] to-[#004B87] px-6 pt-6 pb-4 overflow-hidden">
              {/* Efeitos visuais de movimento */}
              <div className="absolute inset-0 opacity-20">
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-pulse"></div>
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full animate-bounce"></div>
                <div className="absolute -bottom-2 -left-2 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
              </div>

              {/* Botão fechar com círculo - MELHORADO */}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClose}
                className="absolute top-4 right-4 text-white/90 hover:text-white hover:bg-white/20 h-10 w-10 rounded-full bg-white/10 backdrop-blur-sm border-2 border-white/20 hover:border-white/40 transition-all duration-200 hover:scale-110 z-20 shadow-lg"
                aria-label="Fechar formulário"
              >
                <X className="w-5 h-5" />
              </Button>

              <div className="relative z-10 text-center space-y-2 sm:space-y-4">
                {/* Logo LocPay com fundo azul escuro */}
                <div className="flex justify-center mb-2 sm:mb-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-[#003366] rounded-xl blur-sm"></div>
                    <div className="relative bg-[#003366] backdrop-blur-sm rounded-xl px-3 py-1.5 sm:px-4 sm:py-2 border border-[#074377]">
                      <Image
                        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                        alt="LocPay"
                        width={120}
                        height={40}
                        className="h-6 sm:h-8 w-auto"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2 sm:space-y-3">
                  <h2 className="text-xl sm:text-2xl md:text-3xl font-black text-white leading-tight tracking-tight">
                    🚀 Realize seus Projetos com a Antecipação de Aluguel!
                  </h2>
                  <p className="text-xs sm:text-sm md:text-base text-white/90 leading-relaxed font-medium px-2">
                    💰 Preencha os dados e Antecipe até 12 meses de aluguel sem burocracia.
                  </p>
                </div>

                {/* Badge processo digital sem sparkles */}
                <div className="inline-flex items-center gap-1 sm:gap-2 px-3 py-1.5 sm:px-4 sm:py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-xs sm:text-sm font-bold border border-white/30">
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>Processo 100% digital</span>
                  <Zap className="w-3 h-3 sm:w-4 sm:h-4 animate-bounce" />
                </div>
              </div>
            </div>

            {/* Formulário com fundo igual ao popup */}
            <div className="px-4 pb-4 bg-gradient-to-br from-white/90 to-[#F0F7FE]/50 max-h-[60vh] overflow-y-auto">
              <form onSubmit={handleSubmit} className="space-y-4 pt-4">
                {isError && (
                  <div className="flex items-center gap-3 p-4 bg-red-50 border-l-4 border-red-500 rounded-r-2xl text-red-700 animate-in slide-in-from-left-2 duration-300">
                    <AlertCircle className="w-5 h-5 flex-shrink-0" />
                    <span className="text-sm font-medium">Erro ao enviar solicitação. Tente novamente.</span>
                  </div>
                )}

                {/* Nome */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <span>Nome</span>
                    <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    placeholder="Digite seu nome"
                    value={formData.nome}
                    onChange={(e) => handleInputChange("nome", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                      errors.nome
                        ? "border-red-500 focus:border-red-500 shadow-red-100"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                    } focus:ring-0 focus:outline-none focus:shadow-lg`}
                  />
                  {errors.nome && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.nome}
                    </p>
                  )}
                </div>

                {/* Celular */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <span>Celular</span>
                    <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="tel"
                    placeholder="(11) 99999-9999"
                    value={formData.celular}
                    onChange={(e) => handleInputChange("celular", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                      errors.celular
                        ? "border-red-500 focus:border-red-500 shadow-red-100"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                    } focus:ring-0 focus:outline-none focus:shadow-lg`}
                    maxLength={15}
                  />
                  {errors.celular && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.celular}
                    </p>
                  )}
                </div>

                {/* CPF */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <span>CPF</span>
                    <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    placeholder="000.000.000-00"
                    value={formData.cpf}
                    onChange={(e) => handleInputChange("cpf", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                      errors.cpf
                        ? "border-red-500 focus:border-red-500 shadow-red-100"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                    } focus:ring-0 focus:outline-none focus:shadow-lg`}
                    maxLength={14}
                  />
                  {errors.cpf && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.cpf}
                    </p>
                  )}
                </div>

                {/* Aluguel Líquido */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <span>Aluguel Líquido</span>
                    <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    placeholder="R$ 0,00"
                    value={formData.aluguelLiquido}
                    onChange={(e) => handleInputChange("aluguelLiquido", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                      errors.aluguelLiquido
                        ? "border-red-500 focus:border-red-500 shadow-red-100"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                    } focus:ring-0 focus:outline-none focus:shadow-lg`}
                  />
                  {errors.aluguelLiquido && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.aluguelLiquido}
                    </p>
                  )}
                </div>

                {/* Meses Antecipados */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <span>Meses Antecipados</span>
                    <span className="text-red-500">*</span>
                    <span className="text-xs text-gray-500 font-normal">(máx. 12 meses)</span>
                  </label>
                  <Input
                    type="number"
                    placeholder="Ex: 6"
                    min="1"
                    max="12"
                    value={formData.mesesAntecipados}
                    onChange={(e) => handleInputChange("mesesAntecipados", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] placeholder:text-gray-400 ${
                      errors.mesesAntecipados
                        ? "border-red-500 focus:border-red-500 shadow-red-100"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                    } focus:ring-0 focus:outline-none focus:shadow-lg`}
                  />
                  {errors.mesesAntecipados && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.mesesAntecipados}
                    </p>
                  )}
                </div>

                {/* Imobiliária */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <span>Imobiliária</span>
                    <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.imobiliaria}
                    onChange={(e) => handleInputChange("imobiliaria", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-white border-2 rounded-2xl transition-all duration-300 focus:bg-white focus:scale-[1.02] appearance-none cursor-pointer ${
                      errors.imobiliaria
                        ? "border-red-500 focus:border-red-500 shadow-red-100"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-[#80C2F4] focus:shadow-[#00A7E1]/20"
                    } focus:ring-0 focus:outline-none focus:shadow-lg ${!formData.imobiliaria ? "text-gray-400" : "text-gray-900"}`}
                  >
                    <option value="" className="text-gray-400">
                      Selecione a Imobiliária
                    </option>
                    {imobiliarias.map((imobiliaria) => (
                      <option key={imobiliaria} value={imobiliaria} className="text-gray-900">
                        {imobiliaria}
                      </option>
                    ))}
                  </select>
                  {errors.imobiliaria && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.imobiliaria}
                    </p>
                  )}
                </div>

                {/* Upload do Contrato - APENAS PDF */}
                <div className="space-y-2">
                  <label className="block text-sm font-bold text-[#074377] flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    <span>Contrato de Locação (PDF)</span>
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="contrato-upload"
                    />
                    <label
                      htmlFor="contrato-upload"
                      className={`flex items-center justify-center w-full h-24 border-2 border-dashed rounded-2xl cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                        errors.contrato
                          ? "border-red-500 bg-red-50 hover:bg-red-100"
                          : formData.contrato
                            ? "border-green-500 bg-green-50 hover:bg-green-100"
                            : "border-[#80C2F4] bg-[#F0F7FE] hover:bg-[#E8F5FF] hover:border-[#00A7E1]"
                      }`}
                    >
                      <div className="text-center p-4">
                        {formData.contrato ? (
                          <div className="flex flex-col items-center gap-2 text-green-600">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-6 h-6" />
                              <FileText className="w-6 h-6" />
                            </div>
                            <span className="text-sm font-bold">{formData.contrato.name}</span>
                            <span className="text-xs text-green-500">PDF carregado com sucesso!</span>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-2 text-[#074377]">
                            <Upload className="w-8 h-8" />
                            <span className="text-sm font-bold">Clique para fazer upload</span>
                            <span className="text-xs text-gray-500">Apenas PDF – até 20MB</span>
                          </div>
                        )}
                      </div>
                    </label>
                  </div>
                  {errors.contrato && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.contrato}
                    </p>
                  )}
                </div>

                {/* Checkbox de Consentimento */}
                <div className="space-y-2">
                  <div className="flex items-start gap-3 p-4 bg-gray-50 border border-gray-200 rounded-2xl">
                    <input
                      type="checkbox"
                      id="consentimento"
                      checked={formData.consentimento}
                      onChange={(e) => handleInputChange("consentimento", e.target.checked)}
                      className="w-5 h-5 text-[#00A7E1] border-2 border-gray-300 rounded focus:ring-[#00A7E1] focus:ring-2 mt-0.5"
                    />
                    <label htmlFor="consentimento" className="text-sm text-gray-700 leading-relaxed cursor-pointer">
                      <span className="font-semibold text-[#074377]">Autorizo o uso dos meus dados</span> para análise
                      da antecipação conforme nossa{" "}
                      <a href="#" className="text-[#00A7E1] hover:underline font-medium">
                        Política de Privacidade
                      </a>
                      .
                    </label>
                  </div>
                  {errors.consentimento && (
                    <p className="text-red-500 text-xs font-medium flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {errors.consentimento}
                    </p>
                  )}
                </div>

                {/* Botão de envio */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-14 bg-gradient-to-r from-[#074377] via-[#00A7E1] to-[#6BBAED] hover:from-[#0090c0] hover:via-[#0080b0] hover:to-[#5aa8db] text-white font-black text-base rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed mt-6 relative overflow-hidden group"
                >
                  {/* Efeito de brilho no botão */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                  {isLoading ? (
                    <div className="flex items-center justify-center gap-3">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Enviando...</span>
                      <Zap className="w-5 h-5 animate-pulse" />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-3">
                      <span>SOLICITAR ANTECIPAÇÃO</span>
                      <Zap className="w-5 h-5" />
                    </div>
                  )}
                </Button>

                {/* Texto de segurança */}
                <div className="flex items-center justify-center gap-2 pt-3">
                  <Lock className="w-4 h-4 text-[#074377]" />
                  <p className="text-xs text-gray-600 font-medium">
                    Seus dados estão protegidos com criptografia de ponta
                  </p>
                </div>
              </form>
            </div>
          </>
        ) : (
          /* Mensagem de sucesso */
          <div className="p-8 text-center bg-gradient-to-br from-green-50 via-white to-[#F0F7FE]">
            {/* Botão fechar na tela de sucesso também */}
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 h-10 w-10 rounded-full bg-gray-50 border-2 border-gray-200 hover:border-gray-300 transition-all duration-200 hover:scale-110 z-20 shadow-lg"
              aria-label="Fechar formulário"
            >
              <X className="w-5 h-5" />
            </Button>

            <div className="relative mb-6">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto shadow-2xl">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-black text-[#074377] mb-4">Obrigado! 🎉</h3>
            <p className="text-base text-gray-600 mb-6 leading-relaxed font-medium">
              Já estamos analisando a sua antecipação!
            </p>
            <div className="space-y-3 mb-6">
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                <span>Resposta em até 1 hora</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Zap className="w-4 h-4" />
                <span>Processo 100% digital</span>
              </div>
            </div>
            <Button
              onClick={handleClose}
              className="bg-gradient-to-r from-[#074377] to-[#00A7E1] hover:from-[#0090c0] hover:to-[#0080b0] text-white font-bold px-8 py-3 rounded-2xl transition-all duration-300 hover:scale-105 shadow-lg"
            >
              Fechar
            </Button>
          </div>
        )}
      </Card>
    </div>
  )
}

// Declaração global para TypeScript
declare global {
  interface Window {
    locpayFormOpen?: boolean
  }
}
