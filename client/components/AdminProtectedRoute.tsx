"use client"

import React from "react"
import { useAdminAuth } from "@/hooks/useAdminAuth"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertCircle, Shield, RefreshCw, Home } from "lucide-react"

interface AdminProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  fallback?: React.ReactNode
}

export default function AdminProtectedRoute({ 
  children, 
  requiredPermission,
  fallback 
}: AdminProtectedRouteProps) {
  const { 
    isAuthenticated, 
    isAdmin, 
    loading, 
    error, 
    verifyAdminAuth, 
    redirectToLogin,
    hasPermission 
  } = useAdminAuth()

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-2xl shadow-lg max-w-md mx-4">
          <div className="w-16 h-16 border-4 border-gray-200 rounded-full animate-spin mx-auto mb-6">
            <div className="w-full h-full border-4 border-transparent border-t-blue-600 border-r-blue-700 rounded-full animate-spin"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Verificando Permissões</h2>
          <p className="text-gray-600">Validando privilégios de administrador...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-2xl shadow-lg max-w-md mx-4">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-6" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Acesso Negado</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex flex-col gap-3">
            <Button onClick={verifyAdminAuth} className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              Tentar Novamente
            </Button>
            <Button variant="outline" onClick={redirectToLogin} className="w-full">
              <Home className="w-4 h-4 mr-2" />
              Voltar ao Início
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-2xl shadow-lg max-w-md mx-4">
          <Shield className="w-16 h-16 text-gray-400 mx-auto mb-6" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Autenticação Necessária</h2>
          <p className="text-gray-600 mb-6">Você precisa estar logado para acessar esta área.</p>
          <Button onClick={redirectToLogin} className="w-full">
            <Home className="w-4 h-4 mr-2" />
            Fazer Login
          </Button>
        </div>
      </div>
    )
  }

  // Not admin
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-2xl shadow-lg max-w-md mx-4">
          <Shield className="w-16 h-16 text-red-500 mx-auto mb-6" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Privilégios Insuficientes</h2>
          <p className="text-gray-600 mb-6">
            Você não possui privilégios de administrador para acessar esta área.
          </p>
          <Button onClick={redirectToLogin} className="w-full">
            <Home className="w-4 h-4 mr-2" />
            Voltar ao Início
          </Button>
        </div>
      </div>
    )
  }

  // Check specific permission if required
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-2xl shadow-lg max-w-md mx-4">
          <AlertCircle className="w-16 h-16 text-orange-500 mx-auto mb-6" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Permissão Específica Necessária</h2>
          <p className="text-gray-600 mb-6">
            Você não possui a permissão "{requiredPermission}" necessária para acessar esta funcionalidade.
          </p>
          <Button onClick={() => window.history.back()} className="w-full">
            Voltar
          </Button>
        </div>
      </div>
    )
  }

  // All checks passed - render children
  return <>{children}</>
}

// Higher-order component for easier usage
export function withAdminProtection<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission?: string
) {
  return function ProtectedComponent(props: P) {
    return (
      <AdminProtectedRoute requiredPermission={requiredPermission}>
        <Component {...props} />
      </AdminProtectedRoute>
    )
  }
}
