# Este é um exemplo de arquivo .env para o LocPay.
# Certifique-se de substituir os valores pelos corretos para o seu ambiente.

# Database Configuration
# Configurações do banco de dados para o Prisma.
# Certifique-se de que o banco de dados esteja configurado corretamente e acessível.
# O formato da URL do banco de dados é: prisma+postgres://<user>:<password>@<host>:<port>/<database>?sslmode=disable
DATABASE_URL="prisma+postgres://<user>:<password>@<host>:<port>/<database>?sslmode=disable"

# JWT Secret
# Esta chave é usada para assinar e verificar tokens JWT.
# Deve ser uma string longa e complexa para garantir a segurança.
# É recomendável usar uma ferramenta para gerar uma chave segura.
JWT_SECRET="your_jwt_secret"

# N8N Webhook URL - URL do webhook do N8N para receber eventos do LocPay.
# Esta URL deve ser acessível publicamente e configurada no N8N para receber os webhooks.
# Certifique-se de que o N8N esteja configurado para aceitar requisições do LocPay.
# Exemplo: https://n8n.yourdomain.com/webhook ou https://n8n.locpay.com.br/webhook-test/
N8N_WEBHOOK_URL="https://n8n.locpay.com.br/webhook-test/"

# N8N Auth Header - Diferente do JWT_SECRET
# Este é usado para autenticar requisições do N8N para o LocPay.
# É importante que seja diferente do JWT_SECRET usado para autenticação de usuários.
# Gere uma chave segura para este valor.
N8N_AUTH_HEADER="your_n8n_auth_header"

# Google Drive Integration
# Configurações para integração com o Google Drive.
# ID da pasta raiz do Google Drive onde os arquivos serão armazenados.
# Certifique-se de que o serviço de conta do Google Drive tenha as permissões corretas
# para acessar e modificar os arquivos nesta pasta.
# Você pode obter o ID da pasta raiz do Google Drive acessando a pasta no navegador e copiando o ID da URL.
# Exemplo: https://drive.google.com/drive/folders/your-google-drive-root-folder-id
DRIVE_ROOT_FOLDER_ID="your-google-drive-root-folder-id"

# Caminho para o arquivo de conta de serviço do Google Drive.
# Este arquivo JSON contém as credenciais necessárias para autenticar o LocPay com o Google Drive.
# Certifique-se de que o caminho esteja correto e que o arquivo tenha as permissões necessárias
# Gere este arquivo JSON em 7 passos:
# 1. Acesse https://console.cloud.google.com/apis/credentials
# 2. Clique em "Criar credenciais" e selecione "Conta de serviço".
# 3. Preencha os detalhes da conta de serviço e clique em "Criar".
# 4. Na seção "Conceder acesso à conta de serviço", adicione as permissões necessárias (por exemplo, "Proprietário" para acesso total).
# 5. Clique em "Concluir"
# 6. Após criar a conta de serviço, procure por "Chaves" ou "Criar chave" e selecione "JSON" como o tipo de chave.
# 7. Baixe o arquivo JSON e coloque-o aqui.
GOOGLE_SERVICE_ACCOUNT_JSON='{example_json_here}'

# Redis Cache Configuration
# Configurações para o Redis, que é usado para cache, rate limiting e outras funcionalidades.
# Certifique-se de que o Redis esteja instalado e em execução na máquina ou servidor especificado.
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_DB=0
REDIS_THROTTLE_DB=1
REDIS_QUEUE_DB=2
REDIS_POLLING_DB=3
REDIS_N8N_DB=4

# Cache Settings
# Configurações de cache para o Redis.
# CACHE_TTL: Tempo de vida do cache em segundos. Após esse tempo, os itens serão removidos do cache.
# CACHE_MAX_ITEMS: Número máximo de itens que podem ser armazenados no cache.
CACHE_TTL=300
CACHE_MAX_ITEMS=100

# Application Settings
# Configurações gerais da aplicação.
# NODE_ENV: Define o ambiente de execução da aplicação (development, production, etc.).
# PORT: Porta em que a aplicação será executada.
# FRONTEND_URL: URL do frontend da aplicação, usada para redirecionamentos e links
NODE_ENV="development"
PORT=3000
FRONTEND_URL="http://localhost:3000"
