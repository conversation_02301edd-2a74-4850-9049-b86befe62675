"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { AlertTriangle, Mail, Phone } from "lucide-react"

interface RemoveRealEstateModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  realEstateName: string
}

export default function RemoveRealEstateModal({
  open,
  onOpenChange,
  realEstateName,
}: RemoveRealEstateModalProps) {
  const handleClose = () => {
    onOpenChange(false)
  }

  const handleContactDeveloper = () => {
    // You can customize this with actual contact information
    const email = "<EMAIL>"
    const subject = `Solicitação de Remoção - Imobiliária: ${realEstateName}`
    const body = `Olá,

Gostaria de solicitar a remoção da imobiliária "${realEstateName}" do sistema.

Motivo da remoção:
[Descreva o motivo aqui]

Dados da imobiliária:
- Nome: ${realEstateName}
- Data da solicitação: ${new Date().toLocaleDateString("pt-BR")}

Aguardo retorno.

Atenciosamente,
[Seu nome]`

    const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.open(mailtoLink, '_blank')
    
    handleClose()
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-orange-600">
            <AlertTriangle className="h-5 w-5" />
            Remoção de Imobiliária
          </DialogTitle>
          <DialogDescription>
            Solicitação de remoção para: <strong>{realEstateName}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="space-y-2">
                <h4 className="font-semibold text-orange-800">
                  Ação Restrita
                </h4>
                <p className="text-sm text-orange-700">
                  A remoção de imobiliárias é uma operação sensível que pode afetar dados históricos 
                  e solicitações em andamento. Por questões de segurança e integridade dos dados, 
                  esta ação requer validação técnica.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold text-gray-900">
              Para prosseguir com a remoção:
            </h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                Entre em contato com a equipe de desenvolvimento
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                Informe o motivo da remoção
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                Aguarde a validação e processamento
              </li>
            </ul>
          </div>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h5 className="font-medium text-blue-800 mb-2">Contato da Equipe Técnica:</h5>
            <div className="space-y-1 text-sm text-blue-700">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span>(11) 9999-9999</span>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="w-full sm:w-auto"
          >
            Cancelar
          </Button>
          <Button
            type="button"
            onClick={handleContactDeveloper}
            className="w-full sm:w-auto bg-primary hover:bg-primary/90"
          >
            <Mail className="mr-2 h-4 w-4" />
            Enviar E-mail
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
