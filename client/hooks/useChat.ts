"use client"

import { useState, useCallback } from "react"

interface Message {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

interface UserInfo {
  name: string
  email: string
}

interface ConversationMessage {
  role: "user" | "assistant"
  content: string
}

interface ConversationStats {
  totalMessages: number
  userMessages: number
  assistantMessages: number
  conversationStarted: Date | null
  lastMessage: Message | null
}

export function useChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)

  const addMessage = useCallback((content: string, role: "user" | "assistant") => {
    const message: Message = {
      id: Date.now().toString() + Math.random(),
      content,
      role,
      timestamp: new Date(),
    }
    setMessages((prev) => [...prev, message])
    return message
  }, [])

  const sendMessage = useCallback(
    async (content: string): Promise<void> => {
      if (!content.trim() || isLoading) return

      // Adicionar mensagem do usuário
      addMessage(content, "user")
      setIsLoading(true)

      try {
        // Preparar histórico da conversa - últimas 10 mensagens para contexto
        const conversationHistory: ConversationMessage[] = messages
          .slice(-10) // Pegar últimas 10 mensagens
          .map((msg) => ({
            role: msg.role === "user" ? "user" : "assistant",
            content: msg.content,
          }))

        const response = await fetch("/api/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            message: content,
            conversationHistory,
            userInfo,
          }),
        })

        if (!response.ok) {
          throw new Error("Erro na comunicação com o servidor")
        }

        const data = await response.json()
        addMessage(data.response, "assistant")
      } catch (error) {
        console.error("Erro ao enviar mensagem:", error)
        addMessage(
          "Desculpe, ocorreu um erro. Tente novamente ou entre em contato conosco pelo WhatsApp: (85) 99199-2305",
          "assistant",
        )
      } finally {
        setIsLoading(false)
      }
    },
    [messages, isLoading, userInfo, addMessage],
  )

  const clearMessages = useCallback(() => {
    setMessages([])
  }, [])

  const initializeChat = useCallback(() => {
    if (messages.length === 0) {
      addMessage(
        "Oi! Sou a Lia da LocPay! 💰 Antecipe até 12 meses de aluguel em 12h. Como posso te ajudar?",
        "assistant",
      )
    }
  }, [messages.length, addMessage])

  // Função para obter estatísticas da conversa
  const getConversationStats = useCallback((): ConversationStats => {
    return {
      totalMessages: messages.length,
      userMessages: messages.filter((msg) => msg.role === "user").length,
      assistantMessages: messages.filter((msg) => msg.role === "assistant").length,
      conversationStarted: messages.length > 0 ? messages[0].timestamp : null,
      lastMessage: messages.length > 0 ? messages[messages.length - 1] : null,
    }
  }, [messages])

  return {
    messages,
    isLoading,
    userInfo,
    setUserInfo,
    sendMessage,
    clearMessages,
    initializeChat,
    getConversationStats,
  }
}
