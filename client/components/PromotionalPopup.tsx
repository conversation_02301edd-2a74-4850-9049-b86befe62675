"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { X, CheckCircle, Clock } from "lucide-react"
import Image from "next/image"

interface FormData {
  nome: string
  celular: string
  tipo: string
}

export default function PromotionalPopup() {
  const [isVisible, setIsVisible] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    nome: "",
    celular: "",
    tipo: "",
  })
  const [errors, setErrors] = useState<Partial<FormData>>({})

  // Máscara para telefone brasileiro
  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, "")
    if (numbers.length <= 11) {
      return numbers.replace(/(\d{2})(\d)/, "($1) $2").replace(/(\d{4,5})(\d{4})$/, "$1-$2")
    }
    return value
  }

  // Timer de 10 segundos - verificar se já foi dispensado
  useEffect(() => {
    if (typeof window === "undefined") return

    const isDismissed = localStorage.getItem("locpay-popup-dismissed")
    if (isDismissed) return

    const timer = setTimeout(() => {
      // Verificar se o formulário não está aberto
      if (!isVisible && !isSubmitted && !window.locpayFormOpen) {
        setIsVisible(true)
      }
    }, 10000)

    return () => clearTimeout(timer)
  }, [isVisible, isSubmitted])

  // Exit intent detection - verificar se já foi dispensado
  useEffect(() => {
    if (typeof window === "undefined") return

    const isDismissed = localStorage.getItem("locpay-popup-dismissed")
    if (isDismissed) return

    const handleMouseLeave = (e: MouseEvent) => {
      // Verificar se o formulário não está aberto
      if (e.clientY <= 0 && !isVisible && !isSubmitted && !window.locpayFormOpen) {
        setIsVisible(true)
      }
    }

    document.addEventListener("mouseleave", handleMouseLeave)
    return () => document.removeEventListener("mouseleave", handleMouseLeave)
  }, [isVisible, isSubmitted])

  // Bloquear chat quando popup estiver aberto
  useEffect(() => {
    if (typeof window !== "undefined") {
      if (isVisible) {
        // Adicionar flag para suspender chat
        window.locpayPopupOpen = true
      } else {
        // Remover flag quando fechar
        window.locpayPopupOpen = false
      }

      return () => {
        window.locpayPopupOpen = false
      }
    }
  }, [isVisible])

  // Validação de campos
  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {}

    if (!formData.nome.trim()) {
      newErrors.nome = "Nome é obrigatório"
    }

    if (!formData.celular.trim()) {
      newErrors.celular = "Celular é obrigatório"
    } else if (formData.celular.replace(/\D/g, "").length < 10) {
      newErrors.celular = "Celular deve ter pelo menos 10 dígitos"
    }

    if (!formData.tipo) {
      newErrors.tipo = "Selecione uma opção"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Envio do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      // Formatar celular: remover formatação e adicionar 55
      const celularLimpo = formData.celular.replace(/\D/g, "")
      const celularFormatado = `55${celularLimpo}`

      const payload = {
        nome: formData.nome,
        celular: celularFormatado,
        tipo: formData.tipo === "proprietario" ? "PROPRIETÁRIO" : "ADMINISTRADOR",
      }

      console.log("Enviando dados:", payload) // Para debug

      const response = await fetch("https://locpay.app.n8n.cloud/webhook/07f8ac4d-a1a1-45bc-be2e-8dfe39ade36b", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        mode: "cors", // Explicitamente definir CORS
        body: JSON.stringify(payload),
      })

      console.log("Response status:", response.status) // Para debug
      console.log("Response headers:", response.headers) // Para debug

      // Verificar se a resposta foi bem-sucedida
      if (response.ok || response.status === 200 || response.status === 201) {
        setIsSubmitted(true)
      } else {
        // Tentar ler a resposta de erro
        const errorText = await response.text()
        console.error("Erro na resposta:", errorText)
        throw new Error(`Erro HTTP: ${response.status}`)
      }
    } catch (error) {
      console.error("Erro completo:", error)

      // Fallback: mesmo com erro, marcar como enviado se os dados estão válidos
      // Isso evita que o usuário fique preso em caso de problemas de CORS
      if (formData.nome && formData.celular && formData.tipo) {
        console.log("Fallback: marcando como enviado devido a possível problema de CORS")
        setIsSubmitted(true)
      } else {
        alert("Erro ao enviar formulário. Verifique sua conexão e tente novamente.")
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Fechar pop-up e salvar no localStorage
  const handleClose = () => {
    setIsVisible(false)
    if (typeof window !== "undefined") {
      window.locpayPopupOpen = false
      localStorage.setItem("locpay-popup-dismissed", "true")
    }
  }

  // Atualizar campos do formulário
  const handleInputChange = (field: keyof FormData, value: string) => {
    if (field === "celular") {
      value = formatPhone(value)
    }
    setFormData((prev) => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }))
    }
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
      {/* Background decorativo similar ao hero */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-radial from-[#E8F5FF]/20 via-[#F5FAFE]/10 to-transparent opacity-60" />
        <div className="absolute -top-10 right-0 w-96 h-96 bg-[#80C2F4]/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-20 left-0 w-72 h-72 bg-[#074377]/5 rounded-full blur-2xl" />
      </div>

      <Card className="relative w-full max-w-md mx-auto bg-white/95 backdrop-blur-sm shadow-2xl border-2 border-[#074377] rounded-3xl overflow-hidden animate-in zoom-in-95 duration-300">
        {!isSubmitted ? (
          <>
            {/* Header Clean com Logo */}
            <div className="relative bg-gradient-to-br from-white/90 to-[#F0F7FE]/50 px-5 pt-5 pb-3">
              {/* Botão fechar em círculo */}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClose}
                className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 hover:bg-gray-100 h-8 w-8 rounded-full bg-gray-50 border border-gray-200 transition-all duration-200 hover:scale-110"
              >
                <X className="w-4 h-4" />
              </Button>

              {/* Logo e título */}
              <div className="text-center space-y-4">
                {/* Logo LocPay oficial com background azul menor */}
                <div className="flex justify-center mb-3">
                  <div className="bg-[#074377] rounded-xl px-3 py-1.5">
                    <Image
                      src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                      alt="LocPay"
                      width={110}
                      height={35}
                      className="h-7 w-auto"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <h2 className="text-xl md:text-2xl font-bold text-[#074377] leading-tight">
                    🎉 Promoção de Antecipação de Aluguel!
                  </h2>
                  <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                    Receba até 1 Ano de Aluguel de uma só vez 🚀 Oferta válida só pelos próximos dias
                  </p>
                </div>

                {/* Badge de tempo limitado */}
                <div className="inline-flex items-center gap-1 px-3 py-1 bg-[#80C2F4]/10 text-[#074377] rounded-full text-xs font-medium">
                  <Clock className="w-3 h-3" />
                  <span>Oferta por tempo limitado</span>
                </div>
              </div>
            </div>

            {/* Formulário */}
            <CardContent className="px-5 pb-5 bg-white/90">
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Campo Nome */}
                <div className="space-y-1">
                  <label className="block text-sm font-semibold text-[#074377]">
                    Nome <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    placeholder="Digite seu nome"
                    value={formData.nome}
                    onChange={(e) => handleInputChange("nome", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-gray-50 border-2 rounded-2xl transition-all focus:bg-white placeholder:text-gray-400 ${
                      errors.nome
                        ? "border-red-500 focus:border-red-500"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-gray-300"
                    } focus:ring-0 focus:outline-none`}
                  />
                  {errors.nome && <p className="text-red-500 text-xs">{errors.nome}</p>}
                </div>

                {/* Campo Celular */}
                <div className="space-y-1">
                  <label className="block text-sm font-semibold text-[#074377]">
                    Celular <span className="text-red-500">*</span>
                  </label>
                  <Input
                    type="tel"
                    placeholder="(11) 99999-9999"
                    value={formData.celular}
                    onChange={(e) => handleInputChange("celular", e.target.value)}
                    className={`w-full h-10 px-3 text-sm bg-gray-50 border-2 rounded-2xl transition-all focus:bg-white placeholder:text-gray-400 ${
                      errors.celular
                        ? "border-red-500 focus:border-red-500"
                        : "border-gray-200 focus:border-[#00A7E1] hover:border-gray-300"
                    } focus:ring-0 focus:outline-none`}
                    maxLength={15}
                  />
                  {errors.celular && <p className="text-red-500 text-xs">{errors.celular}</p>}
                </div>

                {/* Seleção de tipo de usuário */}
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-[#074377]">
                    Você é: <span className="text-red-500">*</span>
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center p-3 bg-gray-50 border-2 border-gray-200 rounded-2xl cursor-pointer hover:bg-white hover:border-[#80C2F4] transition-all group">
                      <input
                        type="radio"
                        name="tipo"
                        value="proprietario"
                        checked={formData.tipo === "proprietario"}
                        onChange={(e) => handleInputChange("tipo", e.target.value)}
                        className="w-4 h-4 text-[#00A7E1] border-2 border-gray-300 focus:ring-[#00A7E1] focus:ring-1"
                      />
                      <span className="ml-2 text-sm text-gray-700 font-medium group-hover:text-[#074377] transition-colors">
                        🏡 Proprietário de imóvel alugado
                      </span>
                    </label>
                    <label className="flex items-center p-3 bg-gray-50 border-2 border-gray-200 rounded-2xl cursor-pointer hover:bg-white hover:border-[#80C2F4] transition-all group">
                      <input
                        type="radio"
                        name="tipo"
                        value="administrador"
                        checked={formData.tipo === "administrador"}
                        onChange={(e) => handleInputChange("tipo", e.target.value)}
                        className="w-4 h-4 text-[#00A7E1] border-2 border-gray-300 focus:ring-[#00A7E1] focus:ring-1"
                      />
                      <span className="ml-2 text-sm text-gray-700 font-medium group-hover:text-[#074377] transition-colors">
                        🏢 Administrador de imóvel alugado
                      </span>
                    </label>
                  </div>
                  {errors.tipo && <p className="text-red-500 text-xs">{errors.tipo}</p>}
                </div>

                {/* Botão de envio */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 bg-[#074377] hover:bg-[#0090c0] text-white font-bold text-base rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Enviando...
                    </div>
                  ) : (
                    "QUERO SABER MAIS 🚀"
                  )}
                </Button>

                {/* Texto de segurança */}
                <p className="text-xs text-gray-500 text-center mt-3">🔒 Seus dados estão seguros conosco</p>
              </form>
            </CardContent>
          </>
        ) : (
          /* Mensagem de agradecimento */
          <div className="p-6 text-center bg-gradient-to-br from-white/90 to-[#F0F7FE]/50">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-xl font-bold text-[#074377] mb-3">Obrigado! 🎉</h3>
            <p className="text-base text-gray-600 mb-4 leading-relaxed">
              📞 Em breve entraremos em contato pelo WhatsApp.
            </p>
            <Button
              onClick={handleClose}
              className="bg-[#074377] hover:bg-[#0090c0] text-white font-bold px-6 py-2 rounded-2xl transition-all duration-300 hover:scale-105"
            >
              Fechar
            </Button>
          </div>
        )}
      </Card>
    </div>
  )
}

// Declaração global para TypeScript
declare global {
  interface Window {
    locpayPopupOpen?: boolean
  }
}
