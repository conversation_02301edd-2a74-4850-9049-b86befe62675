import { Module } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AuthModule } from './auth/auth.module';
import { RentalAdvanceModule } from './rental-advance/rental-advance.module';
import { RealEstateModule } from './real-estate/real-estate.module';
import { AdminModule } from './admin/admin.module';
import { ErrorInterceptor } from './common/interceptors/error.interceptor';
import { CacheModule } from './cache/cache.module';
import { RateLimitModule } from './rate-limit/rate-limit.module';

@Module({
  imports: [
    CacheModule,
    RateLimitModule,
    AuthModule,
    RentalAdvanceModule,
    RealEstateModule,
    AdminModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorInterceptor,
    },
  ],
})
export class AppModule {}
