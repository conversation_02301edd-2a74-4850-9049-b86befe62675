import Link from "next/link"
import Image from "next/image"
import { Instagram, Linkedin, Phone } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-[#004B87] text-white">
      <div className="container mx-auto px-4 py-6">
        {/* Seção principal - compacta */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 mb-4">
          {/* Logo com borda preservada */}
          <Link href="/" className="flex-shrink-0">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3 hover:bg-white/20 transition-all duration-300">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                alt="LocPay"
                width={120}
                height={40}
                className="h-6 w-auto"
              />
            </div>
          </Link>

          {/* Links centrais - apenas no desktop */}
          <nav className="hidden md:flex items-center gap-6">
            <Link href="#vantagens" className="text-sm text-blue-100 hover:text-white transition-colors font-bold">
              Vantagens
            </Link>
            <Link href="#depoimentos" className="text-sm text-blue-100 hover:text-white transition-colors font-bold">
              Depoimentos
            </Link>
            <Link href="/blog" className="text-sm text-blue-100 hover:text-white transition-colors font-bold">
              Blog
            </Link>
          </nav>

          {/* Contato e Redes */}
          <div className="flex items-center gap-3">
            <a
              href="https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 bg-[#00A7E1] hover:bg-[#0090c0] px-3 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              <Phone className="h-4 w-4" />
              <span className="hidden sm:inline">Contato</span>
            </a>
            <Link
              href="https://instagram.com/locpaybr"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            >
              <Instagram className="h-4 w-4" />
            </Link>
            <Link
              href="https://www.linkedin.com/company/locpay/"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            >
              <Linkedin className="h-4 w-4" />
            </Link>
          </div>
        </div>

        {/* Links mobile - apenas no mobile */}
        <div className="flex md:hidden items-center justify-center gap-6 mb-4">
          <Link href="#vantagens" className="text-sm text-blue-100 hover:text-white transition-colors">
            Vantagens
          </Link>
          <Link href="#depoimentos" className="text-sm text-blue-100 hover:text-white transition-colors">
            Depoimentos
          </Link>
          <Link href="/blog" className="text-sm text-blue-100 hover:text-white transition-colors">
            Blog
          </Link>
        </div>

        {/* Linha divisória sutil */}
        <div className="border-t border-blue-400/20 pt-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-2 text-xs text-blue-100">
            <p className="font-bold">© 2025 LocPay. Todos os direitos reservados.</p>
            <div className="flex items-center gap-4">
              <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                <EMAIL>
              </a>
              <span>•</span>
              <span>Fortaleza, CE</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
