import { Card, CardContent } from "@/components/ui/card"
import { Quote, Star, User, Users, Plane } from "lucide-react"

const Avatar = ({ icon: Icon, bgColor }) => (
  <div className={`w-16 h-16 rounded-full ${bgColor} flex items-center justify-center`}>
    <Icon className="w-8 h-8 text-white" />
  </div>
)

export default function Testimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Proprietário de 3 imóveis",
      text: "A LocPay me ajudou muito! Consegui fazer uma reforma completa nos meus imóveis com a antecipação e aumentei o valor do aluguel em 30%. Processo rápido e equipe super atenciosa!",
      avatar: <Avatar icon={User} bgColor="bg-blue-500" />,
      icon: "🏠",
    },
    {
      name: "<PERSON> Beatriz",
      role: "Investidora Imobiliária",
      text: "Incrível! Antecipei o aluguel de 5 imóveis e pude aproveitar uma oportunidade para comprar mais um com os recursos da antecipação. A LocPay é, sem dúvida, a melhor parceira para quem quer crescer no mercado imobiliário.",
      avatar: <Avatar icon={Users} bgColor="bg-green-500" />,
      icon: "📈",
    },
    {
      name: "Roberto Santos",
      role: "Proprietário Primeira Vez",
      text: "Estava receoso no início, mas a transparência, profissionalismo e agilidade da LocPay me conquistaram. Consegui fazer a viagem dos meus sonhos sem comprometer minha renda mensal. Recomendo!",
      avatar: <Avatar icon={Plane} bgColor="bg-purple-500" />,
      icon: "✈️",
    },
  ]

  return (
    <section id="depoimentos" className="py-8 sm:py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-16">
          <span className="inline-block px-3 sm:px-4 py-1.5 bg-[#00A7E1]/10 text-[#00A7E1] rounded-full text-sm font-medium mb-4">
            Histórias de Sucesso
          </span>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1B3B5A] mb-4">
            O Que Nossos Clientes{" "}
            <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text">
              Dizem Sobre Nós 💬
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            Junte-se a milhares de proprietários satisfeitos que já transformaram seus aluguéis em oportunidades
            incríveis com a LocPay! 🌟
          </p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="hover:shadow-lg transition-all flex flex-col">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {testimonial.avatar}
                  <div className="ml-4">
                    <p className="font-semibold text-[#1B3B5A] text-lg">{testimonial.name}</p>
                    <p className="text-sm text-gray-600 flex items-center">
                      {testimonial.role} {testimonial.icon}
                    </p>
                  </div>
                </div>
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <Quote className="h-8 w-8 text-[#00A7E1] mb-4" />
                <p className="italic mb-6 text-gray-600 text-base sm:text-lg">{testimonial.text}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
