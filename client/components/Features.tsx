"use client"

import { Award, Clock, Coins, FileText } from "lucide-react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import SectionTitle from "./SectionTitle"
import Image from "next/image"

export default function Features() {
  const features = [
    {
      icon: Coins,
      title: "Antecipação Flexível 💰",
      description:
        "Você pode antecipar apenas uma parte do seu aluguel e seguir recebendo mensalmente o restante! A LocPay é do seu jeito",
    },
    {
      icon: FileText,
      title: "Processo Simplificado e Integrado 📄",
      description:
        "Apenas o contrato de locação é necessário. Sem burocracia, análise rápida e descomplicada e tudo feito integrado à sua imobiliária parceira!",
    },
    {
      icon: Clock,
      title: "Pagamento em 1 Hora ⏱️",
      description: "Aprovação e pagamento ultra rápidos. Receba até 1 ano de aluguel em até 1 hora.",
    },
    {
      icon: Award,
      title: "100% Seguro 🔒",
      description:
        "Processo totalmente digital e seguro, com criptografia de ponta a ponta em todas as transações e aprovado por todas as nossas imobiliárias parceiras.",
    },
  ]

  return (
    <section id="vantagens" className="py-16 bg-gradient-to-br from-white to-gray-50">
      <div className="container mx-auto px-4">
        <SectionTitle
          badge="Vantagens LocPay"
          darkText="Por Que Escolher a"
          lightText={
            <span className="inline-flex items-center bg-[#074377] px-4 py-2 rounded-xl">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                alt="LocPay"
                width={120}
                height={40}
                className="h-6 w-auto sm:h-8"
              />
            </span>
          }
          description="A LocPay acelera seus planos, antecipando o que você precisa para viver hoje o que sempre sonhou. ✨"
        />

        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="hover:shadow-lg transition-all border-t-4 border-t-[#00A7E1] group hover:-translate-y-1 duration-300"
            >
              <CardHeader>
                <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-[#00A7E1] to-[#1B3B5A] flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="h-6 w-6 text-white" />
                </div>
                <CardTitle className="text-[#1B3B5A] group-hover:text-[#00A7E1] transition-colors">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
