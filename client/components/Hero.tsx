"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Slider } from "@/components/ui/slider"
import { HelpCircle, Phone, TrendingUp, Clock, Percent } from "lucide-react"
import Image from "next/image"
import AnticipationForm from "./AnticipationForm"

const AnimatedText = () => {
  return (
    <div className="mt-6 text-left">
      <p className="text-2xl sm:text-3xl font-semibold leading-tight text-[#1B3B5A]">
        Antecipação de aluguel{" "}
        <span className="bg-gradient-to-r from-[#80C2F4] to-[#074377] text-transparent bg-clip-text font-bold">
          100% digital
        </span>
      </p>
    </div>
  )
}

export default function Hero() {
  const [rentValue, setRentValue] = useState([1000])
  const [monthsValue, setMonthsValue] = useState([6])
  const [isFormOpen, setIsFormOpen] = useState(false)

  const calculateAnticipation = () => {
    const monthlyRate = 0.055 // 5,5% ao mês
    let totalPresentValue = 0

    for (let month = 1; month <= monthsValue[0]; month++) {
      const discountFactor = Math.pow(1 + monthlyRate, month)
      totalPresentValue += rentValue[0] / discountFactor
    }

    return totalPresentValue
  }

  const handleWhatsAppClick = () => {
    window.open(
      "https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+antecipar+meu+aluguel+com+a+LocPay%21&type=phone_number&app_absent=0",
      "_blank",
    )
  }

  const handleAnticipationClick = () => {
    setIsFormOpen(true)
  }

  return (
    <section className="relative overflow-hidden pt-4 pb-12 bg-[#F0F7FE]">
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-radial from-[#E8F5FF] via-[#F5FAFE] to-[#EEF8FC] opacity-60" />
        <svg className="absolute w-full h-full opacity-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
          <defs>
            <pattern
              id="subtleGrid"
              width="15"
              height="15"
              patternUnits="userSpaceOnUse"
              stroke="#074377"
              strokeWidth="0.25"
            >
              <path d="M 0 0 L 0 15 M 0 0 L 15 0" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#subtleGrid)" />
        </svg>
        <div className="absolute -top-10 right-0 w-96 h-96 bg-[#80C2F4] opacity-10 rounded-full blur-3xl" />
        <div className="absolute -bottom-20 left-0 w-72 h-72 bg-[#074377] opacity-10 rounded-full blur-2xl" />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-6 lg:gap-8 items-center">
          <div className="space-y-6 sm:space-y-8 text-left">
            <span className="inline-block mt-4 px-3 py-1 bg-[#00A7E1]/10 text-[#00A7E1] rounded-full text-sm font-medium">
              Antecipação de Aluguel
            </span>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-[#1B3B5A] leading-tight">
              Antecipe até{" "}
              <span className="bg-gradient-to-r from-[#80C2F4] to-[#074377] text-transparent bg-clip-text">
                12 meses
              </span>{" "}
              de aluguel com a{" "}
              <span className="bg-gradient-to-r from-[#80C2F4] to-[#074377] text-transparent bg-clip-text">LocPay</span>
              ! 🚀
            </h1>
            <AnimatedText />
            <p className="text-base sm:text-lg text-gray-600">
              Transforme seu aluguel futuro em dinheiro imediato. Antecipe seus aluguéis e impulsione seus projetos,
              sonhos e oportunidades.
            </p>

            {/* Estatísticas */}
            <div className="flex flex-wrap gap-3">
              <div className="flex items-center gap-2 bg-white/80 rounded-full py-2 px-4 shadow-sm border border-gray-100">
                <TrendingUp className="w-4 h-4 text-[#00A7E1]" />
                <span className="text-sm font-medium text-[#1B3B5A]">+R$ 7M antecipados</span>
              </div>

              <div className="flex items-center gap-2 bg-white/80 rounded-full py-2 px-4 shadow-sm border border-gray-100">
                <Clock className="w-4 h-4 text-[#00A7E1]" />
                <span className="text-sm font-medium text-[#1B3B5A]">Aprovação em 1 hora</span>
              </div>

              <div className="flex items-center gap-2 bg-white/80 rounded-full py-2 px-4 shadow-sm border border-gray-100">
                <Percent className="w-4 h-4 text-[#00A7E1]" />
                <span className="text-sm font-medium text-[#1B3B5A]">Melhores taxas do mercado</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row justify-start items-center gap-4">
              <Button
                size="lg"
                className="w-full sm:w-auto flex items-center gap-4 bg-[#074377] hover:bg-[#0090c0] text-white font-bold px-6 py-5 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                onClick={handleAnticipationClick}
              >
                Antecipar Meu Aluguel
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
                  alt="LocPay"
                  width={90}
                  height={30}
                  className="h-4 w-auto sm:h-6"
                />
              </Button>
              <Button
                className="w-full sm:w-auto flex items-center gap-2 bg-[#25D366] hover:bg-[#1da954] text-white font-bold px-6 py-5 rounded-full transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                onClick={handleWhatsAppClick}
              >
                <Phone className="h-5 w-5" />
                Falar com Especialista
              </Button>
            </div>
          </div>

          <div className="relative mt-8 lg:mt-0">
            <Card className="p-4 sm:p-6 shadow-2xl border-0 bg-white transition-all duration-300 hover:scale-105 hover:shadow-2xl">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-[#1B3B5A] mb-4">Simulador de Antecipação de Aluguel</h3>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-600 font-medium">Online</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Valor Mensal do Aluguel</label>
                    <Slider
                      value={rentValue}
                      onValueChange={setRentValue}
                      min={500}
                      max={4000}
                      step={100}
                      className="mb-2 bg-[#074377] h-2 rounded-full"
                    />
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400">R$ 500</span>
                      <span className="text-sm font-medium text-[#00A7E1]">R$ {rentValue[0]}</span>
                      <span className="text-sm text-gray-400">R$ 4.000</span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Meses de Antecipação</label>
                    <Slider
                      value={monthsValue}
                      onValueChange={setMonthsValue}
                      min={1}
                      max={12}
                      step={1}
                      className="mb-2 bg-[#074377] h-2 rounded-full"
                    />
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-400">1 mês</span>
                      <span className="text-sm font-medium text-[#00A7E1]">{monthsValue[0]} meses</span>
                      <span className="text-sm text-gray-400">12 meses</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-bold text-[#1B3B5A]">Valor da Antecipação</h3>
                    <HelpCircle className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="flex justify-center items-center py-3 px-4 border border-[#074377] rounded-xl bg-[#E6F3FB] transition-transform duration-300 hover:scale-105">
                    <span className="text-xl sm:text-2xl font-bold text-[#00A7E1]">
                      R${" "}
                      {calculateAnticipation().toLocaleString("pt-BR", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </span>
                  </div>
                  <p className="text-xs sm:text-sm text-gray-500">Sujeito a Análise de Crédito</p>
                </div>
                <Button
                  className="w-full bg-[#074377] hover:bg-[#074377] text-white font-bold text-base py-5 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                  onClick={handleAnticipationClick}
                >
                  Solicitar Antecipação de Aluguel
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Formulário de Antecipação */}
      <AnticipationForm isOpen={isFormOpen} onClose={() => setIsFormOpen(false)} />
    </section>
  )
}
