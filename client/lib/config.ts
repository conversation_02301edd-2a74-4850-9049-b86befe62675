/**
 * Application configuration
 */

export const config = {
  // API Configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_URL || '',
    timeout: 30000, // 30 seconds
  },

  // Backend Configuration
  backend: {
    url: process.env.BACKEND_URL || 'http://localhost:3000',
  },

  // Authentication Configuration
  auth: {
    tokenKey: 'x-auth-state',
    tokenExpiry: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  },

  // Admin Configuration
  admin: {
    defaultPageSize: 20,
    maxPageSize: 100,
  },

  // Environment
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',

  // Feature Flags
  features: {
    adminDashboard: true,
    userSearch: true,
    dataExport: true,
    securityLogging: true,
  },

  // UI Configuration
  ui: {
    defaultTheme: 'light',
    animationDuration: 300,
  },
} as const

export type Config = typeof config
