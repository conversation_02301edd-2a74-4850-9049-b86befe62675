import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheService } from './cache.service';
import { CACHE_KEY_METADATA, CACHE_TTL_METADATA } from './cache.decorator';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private cacheService: CacheService,
    private reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: <PERSON>Handler,
  ): Promise<Observable<any>> {
    const cacheKey = this.reflector.get<string>(
      CACHE_KEY_METADATA,
      context.getHandler(),
    );

    if (!cacheKey) {
      return next.handle();
    }

    const cacheTtl = this.reflector.get<number>(
      CACHE_TTL_METADATA,
      context.getHandler(),
    );

    // Gerar chave dinâmica com parâmetros da requisição
    const request = context.switchToHttp().getRequest();
    const dynamicKey = this.generateDynamicKey(cacheKey, request);

    // Tentar recuperar do cache
    const cachedResult = await this.cacheService.get(dynamicKey);
    if (cachedResult) {
      return of(cachedResult);
    }

    // Se não estiver no cache, executar e armazenar
    return next.handle().pipe(
      tap(async (response) => {
        if (response) {
          await this.cacheService.set(dynamicKey, response, cacheTtl);
        }
      }),
    );
  }

  private generateDynamicKey(template: string, request: any): string {
    let key = template;

    // Substituir {param} pelos valores dos parâmetros
    const params = { ...request.params, ...request.query };
    Object.keys(params).forEach((param) => {
      key = key.replace(`{${param}}`, params[param]);
    });

    // Adicionar ID do usuário se disponível
    if (request.user?.id) {
      key = key.replace('{userId}', request.user.id);
    }

    return key;
  }
}
