import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { RealEstateService } from './real-estate.service';

@Controller('real-estate')
export class RealEstateController {
  constructor(private readonly realEstateService: RealEstateService) {}

  @Get()
  async findAll() {
    return this.realEstateService.findAll();
  }

  @Get(':id')
  async findById(@Param('id') id: string) {
    return this.realEstateService.findById(id);
  }

  @Post()
  async create(@Body() data: { name: string; cnpj: string }) {
    return this.realEstateService.create(data);
  }
}
