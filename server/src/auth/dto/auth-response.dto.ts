export class LoginResponseDto {
  message: string;
  codeSent: boolean;
  expiresAt: string;
  userId?: string; // Apenas para debugs ou logs internos, não expor CPF
}

export class AuthResponseDto {
  user: {
    id: string;
    name: string;
    phone?: string;
    createdAt: string;
  };
  accessToken: string;
  tokenType: string;
  expiresIn: number;
}

export class ResendCodeResponseDto {
  message: string;
  codeSent: boolean;
  expiresAt: string;
  canResendAgainAt: string;
}
