"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Shield, AlertTriangle, Eye, X } from "lucide-react"

export default function AdminSecurityBanner() {
  const [isVisible, setIsVisible] = useState(true)
  const [sessionInfo, setSessionInfo] = useState({
    ip: "Detectando...",
    userAgent: "Detectando...",
    timestamp: new Date().toISOString(),
  })

  useEffect(() => {
    // Get basic session info
    const getUserInfo = async () => {
      try {
        // Get IP from a service (in production, you might want to use your own endpoint)
        const ipResponse = await fetch('https://api.ipify.org?format=json')
        const ipData = await ipResponse.json()
        
        setSessionInfo({
          ip: ipData.ip || "Não detectado",
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        })
      } catch (error) {
        console.error("Failed to get session info:", error)
        setSessionInfo(prev => ({
          ...prev,
          ip: "Não detectado",
        }))
      }
    }

    getUserInfo()
  }, [])

  if (!isVisible) return null

  return (
    <Card className="border-orange-200 bg-orange-50 mb-6">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <Shield className="h-5 w-5 text-orange-600 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-sm font-semibold text-orange-800 mb-1">
                Área Administrativa Segura
              </h3>
              <p className="text-sm text-orange-700 mb-3">
                Você está acessando uma área restrita. Todas as ações são monitoradas e registradas para segurança.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-xs text-orange-600">
                <div className="flex items-center space-x-2">
                  <Eye className="h-3 w-3" />
                  <span>IP: {sessionInfo.ip}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-3 w-3" />
                  <span>Sessão: {new Date(sessionInfo.timestamp).toLocaleTimeString('pt-BR')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="h-3 w-3" />
                  <span>Monitoramento: Ativo</span>
                </div>
              </div>
              
              <div className="mt-3 text-xs text-orange-600">
                <p>
                  <strong>Lembrete de Segurança:</strong> Não compartilhe suas credenciais de administrador. 
                  Sempre faça logout ao terminar de usar o sistema.
                </p>
              </div>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="text-orange-600 hover:text-orange-800 hover:bg-orange-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
