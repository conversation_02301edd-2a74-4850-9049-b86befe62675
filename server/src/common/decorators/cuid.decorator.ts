import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { ValidationUtils } from '../utils/validation.utils';

export function IsCuid(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isCuid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          return typeof value === 'string' && ValidationUtils.validateCuid(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} deve ser um CUID válido`;
        },
      },
    });
  };
}
