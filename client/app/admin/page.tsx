"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import AdminSecurityBanner from "@/components/AdminSecurityBanner"
import { api } from "@/lib/api-handler"
import {
  Users,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  TrendingUp,
  DollarSign,
  RefreshCw,
  AlertCircle,
} from "lucide-react"

interface DashboardStats {
  overview: {
    totalUsers: number
    totalRequests: number
    pendingApprovals: number
    approvedRequests: number
    rejectedRequests: number
    recentRequests: number
    approvalRate: number
    totalApprovedValue: number
  }
  statusBreakdown: Array<{
    status: string
    count: number
  }>
  monthlyStats: Array<{
    month: string
    totalRequests: number
    approvedRequests: number
    totalValue: number
  }>
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  const fetchStats = async () => {
    setLoading(true)
    setError("")

    try {
      const response = await api.adminGet<DashboardStats>("admin/statistics")

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch statistics")
      }

      setStats(response.data!)
    } catch (error) {
      console.error("Error fetching stats:", error)
      setError("Erro ao carregar estatísticas")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatPercentage = (value: number) =>
    `${value.toFixed(1)}%`

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      created: "Criadas",
      pdf_uploaded: "PDF Enviado",
      pdf_extracted: "Dados Extraídos",
      data_confirmed: "Dados Confirmados",
      pending_proposal: "Aguardando Proposta",
      proposal_sent: "Proposta Enviada",
      docs_uploaded: "Documentos Enviados",
      awaiting_review: "Aguardando Revisão",
      approved: "Aprovadas",
      rejected: "Rejeitadas",
      cancelled: "Canceladas",
    }
    return labels[status] || status
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold text-primary">Dashboard</h2>
          <Button disabled className="bg-primary/50">
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            Carregando...
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse shadow-lg">
              <CardContent className="p-6">
                <div className="h-4 bg-primary/20 rounded mb-2"></div>
                <div className="h-8 bg-primary/30 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
          <Button onClick={fetchStats}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Tentar Novamente
          </Button>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!stats) return null

  return (
    <div className="space-y-6">
      {/* Security Banner */}
      <AdminSecurityBanner />

      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold text-primary">Dashboard</h2>
        <Button onClick={fetchStats} className="bg-primary hover:bg-primary/90">
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-primary shadow-lg hover:shadow-xl transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total de Usuários</p>
                <p className="text-3xl font-bold text-primary">{stats.overview.totalUsers}</p>
              </div>
              <div className="p-3 bg-primary/10 rounded-full">
                <Users className="h-8 w-8 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-locpay-green shadow-lg hover:shadow-xl transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total de Solicitações</p>
                <p className="text-3xl font-bold text-locpay-green">{stats.overview.totalRequests}</p>
              </div>
              <div className="p-3 bg-locpay-green/10 rounded-full">
                <FileText className="h-8 w-8 text-locpay-green" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500 shadow-lg hover:shadow-xl transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Aguardando Aprovação</p>
                <p className="text-3xl font-bold text-orange-600">{stats.overview.pendingApprovals}</p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-locpay-green shadow-lg hover:shadow-xl transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Taxa de Aprovação</p>
                <p className="text-3xl font-bold text-locpay-green">{formatPercentage(stats.overview.approvalRate)}</p>
              </div>
              <div className="p-3 bg-locpay-green/10 rounded-full">
                <TrendingUp className="h-8 w-8 text-locpay-green" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-locpay-green shadow-lg hover:shadow-xl transition-shadow duration-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Aprovadas</p>
                <p className="text-3xl font-bold text-locpay-green">{stats.overview.approvedRequests}</p>
              </div>
              <div className="p-3 bg-locpay-green/10 rounded-full">
                <CheckCircle className="h-8 w-8 text-locpay-green" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Rejeitadas</p>
                <p className="text-2xl font-bold text-red-600">{stats.overview.rejectedRequests}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Valor Total Aprovado</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.overview.totalApprovedValue)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Últimos 7 dias</p>
                <p className="text-2xl font-bold text-blue-600">{stats.overview.recentRequests}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Distribuição por Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {stats.statusBreakdown.map((item) => (
              <div key={item.status} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">{getStatusLabel(item.status)}</span>
                <span className="text-lg font-bold text-gray-900">{item.count}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
