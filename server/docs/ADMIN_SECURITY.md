# Segurança do Painel Administrativo - LocPay

## 🔒 Medidas de Segurança Implementadas

### 1. **Autenticação e Autorização Multi-Camada**

#### Backend (Servidor)
- **AdminGuard**: Guard personalizado que verifica privilégios de admin em tempo real
- **Verificação Dupla**: Valida tanto o JWT quanto o status admin no banco de dados
- **Interceptor de Segurança**: Monitora e registra todas as tentativas de acesso admin

#### Frontend (Cliente)
- **Hook useAdminAuth**: Gerencia estado de autenticação admin de forma segura
- **AdminProtectedRoute**: Componente que protege rotas administrativas
- **Verificação Contínua**: Valida privilégios a cada requisição crítica

### 2. **Proteções Contra Interceptação (Wireshark/Man-in-the-Middle)**

#### Medidas Implementadas:
1. **Verificação Server-Side**: Mesmo que alguém intercepte e modifique respostas do frontend, o backend sempre valida privilégios
2. **Token Validation**: Cada requisição admin requer token JWT válido
3. **Database Verification**: Status admin é verificado em tempo real no banco de dados
4. **Rate Limiting**: Proteção contra ataques de força bruta
5. **Logging de Segurança**: Todas as tentativas de acesso são registradas

#### Como Funciona:
```
Cliente → Interceptação Possível → Servidor (Validação Real)
```
- Mesmo que um atacante modifique a resposta do `/api/admin/auth/verify`
- O servidor SEMPRE valida privilégios em CADA requisição admin
- Não há como burlar a segurança apenas modificando respostas HTTP

### 3. **Arquitetura de Segurança**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend        │    │   Database      │
│                 │    │                  │    │                 │
│ useAdminAuth()  │───▶│ AdminGuard       │───▶│ User.isAdmin    │
│ ProtectedRoute  │    │ SecurityInterc.  │    │ User.role       │
│ Token Storage   │    │ JWT Validation   │    │ Real-time Check │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 4. **Endpoints de Segurança**

#### `/api/admin/auth/verify`
- **Propósito**: Verificar privilégios admin em tempo real
- **Segurança**: Consulta banco de dados para status atual
- **Proteção**: AdminGuard + JWT validation

#### Todos os endpoints `/api/admin/*`
- **Proteção**: AdminGuard obrigatório
- **Logging**: AdminSecurityInterceptor registra todas as ações
- **Rate Limiting**: Proteção contra spam/ataques

### 5. **Monitoramento e Logs**

#### Logs de Segurança Automáticos:
- Tentativas de acesso não autorizado
- Ações administrativas realizadas
- Falhas de autenticação
- IPs e user agents suspeitos

#### Exemplo de Log:
```
[AdminSecurityInterceptor] SECURITY INCIDENT: UNAUTHORIZED_ADMIN_ACCESS 
- User: user123, IP: *************, Endpoint: GET /api/admin/statistics
```

### 6. **Proteções Específicas Contra Ataques**

#### A. **Token Hijacking**
- Tokens têm expiração (24h)
- Verificação de privilégios a cada uso
- Logout limpa tokens localmente

#### B. **Session Fixation**
- Tokens são únicos por sessão
- Verificação de integridade do usuário

#### C. **Privilege Escalation**
- Verificação dupla: JWT + Database
- Não há cache de privilégios no frontend
- Status admin pode ser revogado em tempo real

#### D. **Man-in-the-Middle**
- HTTPS obrigatório em produção
- Validação server-side independente do frontend
- Não confiança em dados do cliente

### 7. **Configurações de Produção Recomendadas**

#### Variáveis de Ambiente:
```env
# JWT Secret forte
JWT_SECRET=sua-chave-super-secreta-de-256-bits

# HTTPS obrigatório
FORCE_HTTPS=true

# Rate limiting rigoroso
ADMIN_RATE_LIMIT=10

# Logs de segurança
SECURITY_LOGGING=true
```

#### Headers de Segurança:
```typescript
// Adicionar ao main.ts
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
    },
  },
}));
```

### 8. **Resposta a Incidentes**

#### Detecção Automática:
- Múltiplas tentativas de acesso negado
- Acessos de IPs suspeitos
- Padrões anômalos de uso

#### Ações Automáticas:
- Bloqueio temporário de IP
- Invalidação de tokens suspeitos
- Alertas para administradores

### 9. **Auditoria e Compliance**

#### Registros Mantidos:
- Todas as ações administrativas
- Tentativas de acesso (sucesso/falha)
- Mudanças de privilégios
- Timestamps e metadados

#### Relatórios Disponíveis:
- Atividade admin por período
- Tentativas de acesso não autorizado
- Estatísticas de segurança

### 10. **Limitações e Considerações**

#### O que NÃO pode ser burlado:
- Verificação de privilégios no servidor
- Validação de JWT
- Consultas ao banco de dados
- Rate limiting

#### O que um atacante PODE fazer (e por que não importa):
- Modificar respostas HTTP localmente
- Alterar código JavaScript no browser
- Interceptar e modificar requisições

**Por que não importa**: O servidor SEMPRE valida independentemente do que o cliente envia ou recebe.

### 11. **Testes de Segurança Recomendados**

#### Testes Manuais:
1. Tentar acessar `/admin` sem token
2. Tentar acessar com token de usuário comum
3. Modificar localStorage para simular admin
4. Interceptar e modificar respostas HTTP

#### Testes Automatizados:
```bash
# Teste de acesso não autorizado
curl -X GET http://localhost:3000/api/admin/statistics
# Deve retornar 401

# Teste com token inválido
curl -X GET http://localhost:3000/api/admin/statistics \
  -H "Authorization: Bearer token-falso"
# Deve retornar 401

# Teste com usuário não-admin
curl -X GET http://localhost:3000/api/admin/statistics \
  -H "Authorization: Bearer token-usuario-comum"
# Deve retornar 403
```

## ✅ Conclusão

O sistema implementa múltiplas camadas de segurança que tornam impossível burlar a autenticação admin apenas interceptando tráfego HTTP. A segurança real está no servidor, onde todas as validações críticas acontecem de forma independente do cliente.
