import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Building, Users } from "lucide-react"

export default function PartnershipCTA() {
  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <span className="inline-block px-4 py-1.5 bg-[#00A7E1]/10 text-[#00A7E1] rounded-full text-sm font-medium mb-4">
            Parcerias LocPay
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-[#1B3B5A] mb-4">
            Seja uma Imobiliária{" "}
            <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text">
              Parceira LocPay
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Seja você proprietário ou imobiliária, a LocPay tem a solução ideal para impulsionar seus negócios.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <Card className="hover:shadow-lg transition-all">
            <CardHeader>
              <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-[#00A7E1] to-[#1B3B5A] flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold text-[#1B3B5A]">Para Proprietários</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Maximize o potencial dos seus imóveis e tenha acesso a recursos financeiros quando mais precisar.
              </p>
              <Button
                className="w-full bg-[#00A7E1] hover:bg-[#0090c0] text-white transition-all duration-300 transform hover:scale-105"
                onClick={() =>
                  window.open(
                    "https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0",
                    "_blank",
                  )
                }
              >
                Fale com um Especialista
              </Button>
              <p className="text-sm text-gray-500 italic">
                Sua imobiliária ainda não é parceira? Incentive-os a se juntar à LocPay para melhores benefícios!
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all">
            <CardHeader>
              <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-[#00A7E1] to-[#1B3B5A] flex items-center justify-center mb-4">
                <Building className="h-6 w-6 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold text-[#1B3B5A]">Para Imobiliárias</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                Ofereça mais valor aos seus clientes e aumente sua receita tornando-se um parceiro LocPay.
              </p>
              <Button
                className="w-full bg-[#1B3B5A] hover:bg-[#152f47] text-white transition-all duration-300 transform hover:scale-105"
                onClick={() =>
                  window.open(
                    "https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0",
                    "_blank",
                  )
                }
              >
                Torne-se um Parceiro
              </Button>
              <p className="text-sm text-gray-500 italic">
                Já é parceiro? Compartilhe os benefícios da LocPay com seus proprietários!
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
