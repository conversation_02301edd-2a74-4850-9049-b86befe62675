"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { api } from "@/lib/api-handler"
import { Building, Loader2, AlertCircle, CheckCircle } from "lucide-react"

interface CreateRealEstateModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export default function CreateRealEstateModal({
  open,
  onOpenChange,
  onSuccess,
}: CreateRealEstateModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    cnpj: "",
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  const formatCNPJ = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, "")
    
    // Apply CNPJ mask: XX.XXX.XXX/XXXX-XX
    if (digits.length <= 14) {
      return digits
        .replace(/(\d{2})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1/$2")
        .replace(/(\d{4})(\d)/, "$1-$2")
    }
    
    return value
  }

  const validateCNPJ = (cnpj: string) => {
    const digits = cnpj.replace(/\D/g, "")
    
    if (digits.length !== 14) {
      return "CNPJ deve ter 14 dígitos"
    }

    // Check if all digits are the same
    if (/^(\d)\1+$/.test(digits)) {
      return "CNPJ inválido"
    }

    // CNPJ validation algorithm
    let sum = 0
    let weight = 2
    
    // First verification digit
    for (let i = 11; i >= 0; i--) {
      sum += parseInt(digits[i]) * weight
      weight = weight === 9 ? 2 : weight + 1
    }
    
    let remainder = sum % 11
    const firstDigit = remainder < 2 ? 0 : 11 - remainder
    
    if (parseInt(digits[12]) !== firstDigit) {
      return "CNPJ inválido"
    }
    
    // Second verification digit
    sum = 0
    weight = 2
    
    for (let i = 12; i >= 0; i--) {
      sum += parseInt(digits[i]) * weight
      weight = weight === 9 ? 2 : weight + 1
    }
    
    remainder = sum % 11
    const secondDigit = remainder < 2 ? 0 : 11 - remainder
    
    if (parseInt(digits[13]) !== secondDigit) {
      return "CNPJ inválido"
    }
    
    return null
  }

  const handleInputChange = (field: string, value: string) => {
    if (field === "cnpj") {
      value = formatCNPJ(value)
    }
    
    setFormData(prev => ({ ...prev, [field]: value }))
    setError("")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setLoading(true)

    try {
      // Validate form
      if (!formData.name.trim()) {
        throw new Error("Nome da imobiliária é obrigatório")
      }

      if (!formData.cnpj.trim()) {
        throw new Error("CNPJ é obrigatório")
      }

      const cnpjError = validateCNPJ(formData.cnpj)
      if (cnpjError) {
        throw new Error(cnpjError)
      }

      // Submit to API
      const response = await api.adminPost("admin/real-estates", {
        name: formData.name.trim(),
        cnpj: formData.cnpj.replace(/\D/g, ""), // Send only digits
      })

      if (!response.success) {
        throw new Error(response.error || "Erro ao criar imobiliária")
      }

      setSuccess(true)
      setTimeout(() => {
        setSuccess(false)
        onSuccess()
        handleClose()
      }, 1500)

    } catch (error: any) {
      setError(error.message || "Erro ao criar imobiliária")
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({ name: "", cnpj: "" })
    setError("")
    setSuccess(false)
    onOpenChange(false)
  }

  if (success) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="flex flex-col items-center justify-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Imobiliária Criada!
            </h3>
            <p className="text-gray-600 text-center">
              A imobiliária foi cadastrada com sucesso.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-primary">
            <Building className="h-5 w-5" />
            Nova Imobiliária
          </DialogTitle>
          <DialogDescription>
            Cadastre uma nova imobiliária parceira no sistema.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome da Imobiliária</Label>
            <Input
              id="name"
              placeholder="Ex: Imobiliária ABC Ltda"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              disabled={loading}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="cnpj">CNPJ</Label>
            <Input
              id="cnpj"
              placeholder="00.000.000/0000-00"
              value={formData.cnpj}
              onChange={(e) => handleInputChange("cnpj", e.target.value)}
              disabled={loading}
              maxLength={18}
              required
            />
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-primary hover:bg-primary/90"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Criando...
                </>
              ) : (
                <>
                  <Building className="mr-2 h-4 w-4" />
                  Criar Imobiliária
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
