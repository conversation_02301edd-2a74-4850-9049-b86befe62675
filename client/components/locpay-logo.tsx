import type React from "react"

// Certifique-se que a fonte aqui é a mesma usada no restante do site.
// Se o site usa "Inter", por exemplo, mantenha ou ajuste.
// A cor padrão do logo no site parece ser #074377 ou um azul escuro similar.
export const LocPayLogo = ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (
  <svg
    viewBox="0 0 180 40" // Ajustado para caber "LocPay"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <text
      x="10"
      y="30"
      fontFamily="Inter, Arial, sans-serif" // Usando Inter como exemplo, ajuste se necessário
      fontSize="30" // Ajustado para melhor visualização
      fontWeight="bold"
      fill="#074377" // Cor do logo principal da LocPay
    >
      LocPay
    </text>
  </svg>
)
