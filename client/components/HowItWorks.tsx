"use client"

import { useState } from "react"
import { Calculator, FileText, Clock, Wallet } from "lucide-react"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import AnticipationForm from "./AnticipationForm"

export default function HowItWorks() {
  const [isFormOpen, setIsFormOpen] = useState(false)

  const steps = [
    {
      icon: Calculator,
      title: "Simule Agora 🧮",
      description: "Veja quanto você pode receber agora pelos seus aluguéis do futuro",
      highlight: "Simulação gratuita e sem compromisso",
    },
    {
      icon: FileText,
      title: "Envie seus Documentos 📋",
      description: "Processo simplificado: apenas CPF e contrato de aluguel",
      highlight: "100% digital",
    },
    {
      icon: Clock,
      title: "Análise Rápida ⚡",
      description: "Nossa equipe analisa sua solicitação em tempo recorde",
      highlight: "Resposta em até 12h",
    },
    {
      icon: Wallet,
      title: "Receba seu Din<PERSON>iro 💸",
      description: "Valor aprovado depositado diretamente na sua conta",
      highlight: "Transferência imediata",
    },
  ]

  const handleWhatsAppClick = () => {
    window.open(
      "https://api.whatsapp.com/send/?phone=%2B5585991992305&text=Vim+pelo+site+e+quero+transformar+meus+alugueis+futuros+em+dinheiro+agora+por+meio+da+LocPay%21&type=phone_number&app_absent=0",
      "_blank",
    )
  }

  const handleAnticipationClick = () => {
    setIsFormOpen(true)
  }

  return (
    <section className="py-8 sm:py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-16">
          <span className="inline-block px-3 sm:px-4 py-1.5 bg-[#00A7E1]/10 text-[#00A7E1] rounded-full text-sm font-medium mb-4">
            Processo Simplificado
          </span>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1B3B5A] mb-4">
            Seu Aluguel Antecipado em{" "}
            <span className="bg-gradient-to-r from-[#00A7E1] to-[#1B3B5A] text-transparent bg-clip-text">
              4 Passos Simples
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Desbloqueie o potencial do seu aluguel com um processo rápido e descomplicado. 🚀
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <div
              key={index}
              className="relative group transition-transform duration-300 hover:scale-105 hover:rotate-3 w-full min-h-[250px] sm:min-h-[300px]"
            >
              <span className="absolute top-2 right-2 text-4xl font-bold text-gray-500 opacity-20 pointer-events-none mix-blend-multiply z-20">
                {index + 1}
              </span>

              <div className="p-0.5 rounded-lg bg-gradient-to-br from-[#00A7E1] to-[#1B3B5A] h-full w-full">
                <div className="bg-white rounded-lg p-5 sm:p-6 flex flex-col items-center text-center space-y-4 h-full relative z-10">
                  <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center shadow-lg">
                    <step.icon className="w-8 h-8 text-[#1B3B5A]" />
                  </div>
                  <h3 className="text-xl font-bold text-[#1B3B5A]">{step.title}</h3>
                  <span className="inline-block px-3 py-1 bg-[#00A7E1]/10 text-[#00A7E1] rounded-full text-xs font-medium">
                    {step.highlight}
                  </span>
                  <p className="text-gray-600 leading-relaxed">{step.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 flex justify-center">
          <Button
            size="lg"
            className="flex items-center gap-4 bg-[#074377] hover:bg-[#0090c0] text-white font-bold px-6 py-5 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl"
            onClick={handleAnticipationClick}
          >
            Antecipar Agora!
            <Image
              src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Marca%20d'agua-52nAtl2sZqQVf7puMShdyljk3DsiXv.png"
              alt="LocPay"
              width={90}
              height={30}
              className="h-5 w-auto sm:h-7"
            />
          </Button>
        </div>
      </div>

      {/* Formulário de Antecipação */}
      <AnticipationForm isOpen={isFormOpen} onClose={() => setIsFormOpen(false)} />
    </section>
  )
}
