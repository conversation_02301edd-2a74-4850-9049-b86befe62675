import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { AuthControllerV2 } from './auth-v2.controller';
import { PrismaService } from '../prisma.service';
import { N8nService } from '../integrations/n8n/n8n.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'locpay-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [AuthController, AuthControllerV2],
  providers: [AuthService, PrismaService, N8nService, JwtAuthGuard],
  exports: [AuthService, JwtAuthGuard],
})
export class AuthModule {}
