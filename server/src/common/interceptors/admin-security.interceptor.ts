import {
  Injectable,
  NestInterceptor,
  <PERSON>ecution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ForbiddenException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { PrismaService } from '../../prisma.service';

@Injectable()
export class AdminSecurityInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AdminSecurityInterceptor.name);

  constructor(private readonly prisma: PrismaService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const user = request.user;
    const userAgent = request.headers['user-agent'] || 'Unknown';
    const ip = request.ip || request.connection.remoteAddress || 'Unknown';
    const endpoint = `${request.method} ${request.url}`;

    // Log admin access attempt
    this.logger.log(
      `Admin access attempt: ${endpoint} by user ${user?.id || 'anonymous'} from IP ${ip}`,
    );

    // Additional security checks
    if (user && (!user.isAdmin && user.role !== 'admin')) {
      this.logger.warn(
        `Unauthorized admin access attempt by user ${user.id} (${user.name}) from IP ${ip} to ${endpoint}`,
      );
      
      // Log security incident to database (optional)
      this.logSecurityIncident(user.id, ip, endpoint, userAgent, 'UNAUTHORIZED_ADMIN_ACCESS');
      
      throw new ForbiddenException('Acesso negado. Privilégios de administrador necessários.');
    }

    return next.handle().pipe(
      tap(() => {
        // Log successful admin action
        if (user) {
          this.logger.log(
            `Admin action completed: ${endpoint} by ${user.name} (${user.id})`,
          );
        }
      }),
      catchError((error) => {
        // Log failed admin action
        if (user) {
          this.logger.error(
            `Admin action failed: ${endpoint} by ${user.name} (${user.id}) - ${error.message}`,
          );
        }
        throw error;
      }),
    );
  }

  private async logSecurityIncident(
    userId: string,
    ip: string,
    endpoint: string,
    userAgent: string,
    incidentType: string,
  ) {
    try {
      // You could create a SecurityLog model for this
      // For now, we'll just log to console and could extend to database
      this.logger.warn(
        `SECURITY INCIDENT: ${incidentType} - User: ${userId}, IP: ${ip}, Endpoint: ${endpoint}, UserAgent: ${userAgent}`,
      );
      
      // Optional: Store in database for security monitoring
      // await this.prisma.securityLog.create({
      //   data: {
      //     userId,
      //     ip,
      //     endpoint,
      //     userAgent,
      //     incidentType,
      //     timestamp: new Date(),
      //   },
      // });
    } catch (error) {
      this.logger.error('Failed to log security incident:', error);
    }
  }
}
